=== External Importer ===
Contributors: keywordrush.com

Extract product data from eCommerce sites and import into WooCommerce.

== Installation ==

**Requirements**
* PHP version 5.6 or greater
* The most current release of WordPress
* The most current release of WooCommerce plugin (free)

== Changelog ==

== 2.14.0 ==
* New: Added support for importing extracted product tags.
* New: Enabled AI-powered generation and translation of product attributes.

== 2.13.2 ==
* Improvement: Improved handling of long global attributes.
* Improvement: Enhanced processing of stock quantity values.

== 2.13.0 ==
* New: Full support for WooCommerce variations with custom parsers.

== 2.12.0 ==
* New: Added support for OpenRouter, a unified interface for LLMs.
* New: Added AI models – gpt-4.5-preview, claude-3-7-sonnet-latest.

== 2.11.8 ==
* Fix: WooCommerce brand taxonomy import issue resolved.

== 2.11.0 ==
* New: Added a sync option for custom fields.

== 2.10.0 ==
* New: GTIN/EAN import.
* New: Brand import.

== 2.9.7 ==
* Improvement: Added support for Claude 3.5 haiku.

= 2.9.6 =
* Improvement: Added %short_description% placeholder for custom prompts.

= 2.9.3 =
* Fix: Parser fixes.

= 2.9.0 =
* New: Support for GPT-4o mini model added.

= 2.8.0 -
* New: Support for Claude 3.5 Sonnet added.

= 2.7.0 =
* New: AI text generator: Markdown support.
* New: Added booking.com advanced parser.

= 2.6.0 =
* New: Adaptive AI Parsers added.

= 2.5.0 =
* New: Support for GPT-4o model added.

= 2.4.0 =
* New: Support for GPT-4 AI models added.
* New: Support for Claude 3 AI models added.

= 2.3.0 =
* New: Support for the Scrapeowl service has been added.

= 2.2.0 =
* New: Functionality to import/sync stock quantity for the simple product type (requires custom parsers).

= 2.1.0 =
* New: AI generator for reviews (comments).
* New: Custom prompts feature for AI generator.

= 2.0.0 =
* New: Introduced AI features for rewriting and generating unique product content.

= 1.11.2 =
* Improvement: Backorder stock status.

= 1.11.0 =
* New: Import option: Product URL type.

= 1.10.0 =
* New: Import option: Average rating.
* New: Import option: Review сount.

= 1.9.11 =
* Improvement: Scrapping services: Passthrough custom headers.
* Fix: Parser fixes.

= 1.9.8 =
* New: shopee.pl advanced parser.

= 1.9.5 =
* New: Basic variation features (custom parsers only).

= 1.9.3 =
* New: Automatic integration with Content Egg modules for Ebay, Amazon and Aliexpress sources.

= 1.9.0 =
* New: Added support for Scraperapi service.
* New: Added support for Proxycrawl service.
* New: Added support for Scrapingdog service.

= 1.8.0 =
* New: Dropshipping settings: Round up to nearest 50/100.

= 1.7.1 =
* New: Added support for short descriptions (custom parsers only).

= 1.7.0 =
* New: Alibaba.com advanced parser.

= 1.6.4 =
* Fix: Parser fixes.

= 1.6.3 =
* New: Aliexpress.com advanced parser (beta).

= 1.6.0 =
* New: Structured parsers: Support for SKU, MPN, GTIN.
* New: Import settings: Import or autogenerate SKU.

= 1.5.3 =
* Fix: Parser fixes.

= 1.5.0 =
* New: Edx.org advanced parser.
* New: Coursera.org advanced parser.

= 1.4.0 =
* New: Fiverr.com advanced parser.

= 1.3.0 =
* New: Udemy.com advanced parser.
* Improvement: Ability to set an array for custom fields.

= 1.2.0 =
* New: Product title template.
* New: Product body template.
* Fix: Price update for some discounted products.

= 1.1.7 =
* Improvement: Minor changes and parser fixes.

= 1.0.0 =
* Initial version. 