/*!
 * Bootstrap v3.3.4 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */

.egg-container {
  /* font-family: sans-serif; */
  -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
}
.egg-container {
  margin: 0;
}
.egg-container input[type="button"],
.egg-container input[disabled] {
  cursor: default;
}
.egg-container .thumbnail > img,
.egg-container .thumbnail a > img,
.egg-container .img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.egg-container {
  /*! normalize.css v3.0.2 | MIT License | git.io/normalize */
  /*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
}
.egg-container html {
  /* font-family: sans-serif; */
  -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
}
.egg-container body {
  margin: 0;
}
.egg-container article,
.egg-container aside,
.egg-container details,
.egg-container figcaption,
.egg-container figure,
.egg-container footer,
.egg-container header,
.egg-container hgroup,
.egg-container main,
.egg-container menu,
.egg-container nav,
.egg-container section,
.egg-container summary {
  display: block;
}
.egg-container audio,
.egg-container canvas,
.egg-container progress,
.egg-container video {
  display: inline-block;
  vertical-align: baseline;
}
.egg-container audio:not([controls]) {
  display: none;
  height: 0;
}
.egg-container [hidden],
.egg-container template {
  display: none;
}
.egg-container a {
  background-color: transparent;
}
.egg-container a:active,
.egg-container a:hover {
  outline: 0;
}
.egg-container abbr[title] {
  border-bottom: 1px dotted;
}
.egg-container b,
.egg-container strong {
  font-weight: bold;
}
.egg-container dfn {
  font-style: italic;
}
.egg-container h1 {
  margin: .67em 0;
  font-size: 2em;
}
.egg-container mark {
  color: #000;
  background: #ff0;
}
.egg-container small {
  font-size: 80%;
}
.egg-container sub,
.egg-container sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
.egg-container sup {
  top: -.5em;
}
.egg-container sub {
  bottom: -.25em;
}
.egg-container img {
  border: 0;
}
.egg-container svg:not(:root) {
  overflow: hidden;
}
.egg-container figure {
  margin: 1em 40px;
}
.egg-container hr {
  height: 0;
  -webkit-box-sizing: content-box;
     -moz-box-sizing: content-box;
          box-sizing: content-box;
}
.egg-container pre {
  overflow: auto;
}
.egg-container code,
.egg-container kbd,
.egg-container pre,
.egg-container samp {
  /* font-family: monospace, monospace; */
  font-size: 1em;
}
.egg-container button,
.egg-container input,
.egg-container optgroup,
.egg-container select,
.egg-container textarea {
  margin: 0;
  font: inherit;
  color: inherit;
}
.egg-container button {
  overflow: visible;
}
.egg-container button,
.egg-container select {
  text-transform: none;
}
.egg-container button,
.egg-container html input[type="button"],
.egg-container input[type="reset"],
.egg-container input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
.egg-container button[disabled],
.egg-container html input[disabled] {
  cursor: default;
}
.egg-container button::-moz-focus-inner,
.egg-container input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
.egg-container input {
  line-height: normal;
}
.egg-container input[type="checkbox"],
.egg-container input[type="radio"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
  padding: 0;
}
.egg-container input[type="number"]::-webkit-inner-spin-button,
.egg-container input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
.egg-container input[type="search"] {
  -webkit-box-sizing: content-box;
     -moz-box-sizing: content-box;
          box-sizing: content-box;
  -webkit-appearance: textfield;
}
.egg-container input[type="search"]::-webkit-search-cancel-button,
.egg-container input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
.egg-container fieldset {
  padding: .35em .625em .75em;
  margin: 0 2px;
  border: 1px solid #c0c0c0;
}
.egg-container legend {
  padding: 0;
  border: 0;
}
.egg-container textarea {
  overflow: auto;
}
.egg-container optgroup {
  font-weight: bold;
}
.egg-container table {
  border-spacing: 0;
  border-collapse: collapse;
}
.egg-container td,
.egg-container th {
  padding: 0;
}
@media print {
  .egg-container *,
  .egg-container *:before,
  .egg-container *:after {
    color: #000 !important;
    text-shadow: none !important;
    background: transparent !important;
    -webkit-box-shadow: none !important;
            box-shadow: none !important;
  }
  .egg-container a,
  .egg-container a:visited {
    text-decoration: underline;
  }
  .egg-container a[href]:after {
    content: " (" attr(href) ")";
  }
  .egg-container abbr[title]:after {
    content: " (" attr(title) ")";
  }
  .egg-container a[href^="#"]:after,
  .egg-container a[href^="javascript:"]:after {
    content: "";
  }
  .egg-container pre,
  .egg-container blockquote {
    border: 1px solid #999;

    page-break-inside: avoid;
  }
  .egg-container thead {
    display: table-header-group;
  }
  .egg-container tr,
  .egg-container img {
    page-break-inside: avoid;
  }
  .egg-container img {
    max-width: 100% !important;
  }
  .egg-container p,
  .egg-container h2,
  .egg-container h3 {
    orphans: 3;
    widows: 3;
  }
  .egg-container h2,
  .egg-container h3 {
    page-break-after: avoid;
  }
  .egg-container select {
    background: #fff !important;
  }
  .egg-container .navbar {
    display: none;
  }
  .egg-container .btn > .caret,
  .egg-container .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .egg-container .label {
    border: 1px solid #000;
  }
  .egg-container .table {
    border-collapse: collapse !important;
  }
  .egg-container .table td,
  .egg-container .table th {
    background-color: #fff !important;
  }
  .egg-container .table-bordered th,
  .egg-container .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
.egg-container * {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.egg-container *:before,
.egg-container *:after {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.egg-container input,
.egg-container button,
.egg-container select,
.egg-container textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.egg-container a {
  text-decoration: none;
}
.egg-container a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.egg-container figure {
  margin: 0;
}
.egg-container img {
  vertical-align: middle;
}
.egg-container .img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.egg-container .img-rounded {
  border-radius: 6px;
}
.egg-container .img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 4px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all .2s ease-in-out;
       -o-transition: all .2s ease-in-out;
          transition: all .2s ease-in-out;
}
.egg-container .img-circle {
  border-radius: 50%;
}
.egg-container hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eee;
}
.egg-container .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.egg-container .sr-only-focusable:active,
.egg-container .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
.egg-container [role="button"] {
  cursor: pointer;
}
.egg-container h1,
.egg-container h2,
.egg-container h3,
.egg-container h4,
.egg-container h5,
.egg-container h6,
.egg-container .h1,
.egg-container .h2,
.egg-container .h3,
.egg-container .h4,
.egg-container .h5,
.egg-container .h6 {
  color: inherit;
}
.egg-container h1 small,
.egg-container h2 small,
.egg-container h3 small,
.egg-container h4 small,
.egg-container h5 small,
.egg-container h6 small,
.egg-container .h1 small,
.egg-container .h2 small,
.egg-container .h3 small,
.egg-container .h4 small,
.egg-container .h5 small,
.egg-container .h6 small,
.egg-container h1 .small,
.egg-container h2 .small,
.egg-container h3 .small,
.egg-container h4 .small,
.egg-container h5 .small,
.egg-container h6 .small,
.egg-container .h1 .small,
.egg-container .h2 .small,
.egg-container .h3 .small,
.egg-container .h4 .small,
.egg-container .h5 .small,
.egg-container .h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #777;
}
.egg-container h1 small,
.egg-container .h1 small,
.egg-container h2 small,
.egg-container .h2 small,
.egg-container h3 small,
.egg-container .h3 small,
.egg-container h1 .small,
.egg-container .h1 .small,
.egg-container h2 .small,
.egg-container .h2 .small,
.egg-container h3 .small,
.egg-container .h3 .small {
  font-size: 65%;
}
.egg-container h4,
.egg-container .h4,
.egg-container h5,
.egg-container .h5,
.egg-container h6,
.egg-container .h6 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.egg-container h4 small,
.egg-container .h4 small,
.egg-container h5 small,
.egg-container .h5 small,
.egg-container h6 small,
.egg-container .h6 small,
.egg-container h4 .small,
.egg-container .h4 .small,
.egg-container h5 .small,
.egg-container .h5 .small,
.egg-container h6 .small,
.egg-container .h6 .small {
  font-size: 75%;
}
.egg-container h1,
.egg-container .h1 {
  font-size: 36px;
}
.egg-container h2,
.egg-container .h2 {
  font-size: 27px;
}
.egg-container h3,
.egg-container .h3 {
  font-size: 23px;
}
.egg-container h4,
.egg-container .h4 {
  font-size: 18px;
}
.egg-container h5,
.egg-container .h5 {
  font-size: 14px;
}
.egg-container h6,
.egg-container .h6 {
  font-size: 12px;
}
.egg-container p {
  margin: 0 0 18px;
}
.egg-container .lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .egg-container .lead {
    font-size: 21px;
  }
}
.egg-container small,
.egg-container .small {
  font-size: 85%;
}
.egg-container mark,
.egg-container .mark {
  padding: .2em;
  background-color: #fcf8e3;
}
.egg-container .text-left {
  text-align: left;
}
.egg-container .text-right {
  text-align: right;
}
.egg-container .text-center {
  text-align: center;
}
.egg-container .text-justify {
  text-align: justify;
}
.egg-container .text-nowrap {
  white-space: nowrap;
}
.egg-container .text-lowercase {
  text-transform: lowercase;
}
.egg-container .text-uppercase {
  text-transform: uppercase;
}
.egg-container .text-capitalize {
  text-transform: capitalize;
}
.egg-container .text-muted {
  color: #777;
}
.egg-container .text-primary {
  color: #337ab7;
}
a.egg-container .text-primary:hover {
  color: #286090;
}
.egg-container .text-success {
  color: #3c763d;
}
a.egg-container .text-success:hover {
  color: #2b542c;
}
.egg-container .text-info {
  color: #31708f;
}
a.egg-container .text-info:hover {
  color: #245269;
}
.egg-container .text-warning {
  color: #8a6d3b;
}
a.egg-container .text-warning:hover {
  color: #66512c;
}
.egg-container .text-danger {
  color: #a94442;
}
a.egg-container .text-danger:hover {
  color: #843534;
}
.egg-container .bg-primary {
  color: #fff;
  background-color: #337ab7;
}
a.egg-container .bg-primary:hover {
  background-color: #286090;
}
.egg-container .bg-success {
  background-color: #dff0d8;
}
a.egg-container .bg-success:hover {
  background-color: #c1e2b3;
}
.egg-container .bg-info {
  background-color: #d9edf7;
}
a.egg-container .bg-info:hover {
  background-color: #afd9ee;
}
.egg-container .bg-warning {
  background-color: #fcf8e3;
}
a.egg-container .bg-warning:hover {
  background-color: #f7ecb5;
}
.egg-container .bg-danger {
  background-color: #f2dede;
}
a.egg-container .bg-danger:hover {
  background-color: #e4b9b9;
}
.egg-container .page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eee;
}
.egg-container ul,
.egg-container ol {
  margin-top: 0;
  margin-bottom: 10px;
}
.egg-container ul ul,
.egg-container ol ul,
.egg-container ul ol,
.egg-container ol ol {
  margin-bottom: 0;
}
.egg-container .list-unstyled {
  padding-left: 0;
  list-style: none;
}
.egg-container .list-inline {
  padding-left: 0;
  margin-left: -5px;
  list-style: none;
}
.egg-container .list-inline > li {
  display: inline-block;
  padding-right: 5px;
  padding-left: 5px;
}
.egg-container dl {
  margin-top: 0;
  margin-bottom: 20px;
}
.egg-container dt,
.egg-container dd {
  line-height: 1.42857143;
}
.egg-container dt {
  font-weight: bold;
}
.egg-container dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .egg-container .dl-horizontal dt {
    float: left;
    width: 160px;
    overflow: hidden;
    clear: left;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .egg-container .dl-horizontal dd {
    margin-left: 180px;
  }
}
.egg-container abbr[title],
.egg-container abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777;
}
.egg-container .initialism {
  font-size: 90%;
  text-transform: uppercase;
}
.egg-container address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857143;
}
.egg-container code,
.egg-container kbd,
.egg-container pre,
.egg-container samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
.egg-container code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
.egg-container kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25);
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .25);
}
.egg-container kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: bold;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.egg-container pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857143;
  color: #333;
  word-break: break-all;
  word-wrap: break-word;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.egg-container pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.egg-container .pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.egg-container .container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 768px) {
  .egg-container .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .egg-container .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .egg-container .container {
    width: 1170px;
  }
}
.egg-container .container-fluid {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}
.egg-container .row {
  margin-right: -15px;
  margin-left: -15px;
  margin-bottom: 30px
}
.egg-container .col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.egg-container .col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left;
}
.egg-container .col-xs-12 {
  width: 100%;
}
.egg-container .col-xs-11 {
  width: 91.66666667%;
}
.egg-container .col-xs-10 {
  width: 83.33333333%;
}
.egg-container .col-xs-9 {
  width: 75%;
}
.egg-container .col-xs-8 {
  width: 66.66666667%;
}
.egg-container .col-xs-7 {
  width: 58.33333333%;
}
.egg-container .col-xs-6 {
  width: 50%;
}
.egg-container .col-xs-5 {
  width: 41.66666667%;
}
.egg-container .col-xs-4 {
  width: 33.33333333%;
}
.egg-container .col-xs-3 {
  width: 25%;
}
.egg-container .col-xs-2 {
  width: 16.66666667%;
}
.egg-container .col-xs-1 {
  width: 8.33333333%;
}
.egg-container .col-xs-pull-12 {
  right: 100%;
}
.egg-container .col-xs-pull-11 {
  right: 91.66666667%;
}
.egg-container .col-xs-pull-10 {
  right: 83.33333333%;
}
.egg-container .col-xs-pull-9 {
  right: 75%;
}
.egg-container .col-xs-pull-8 {
  right: 66.66666667%;
}
.egg-container .col-xs-pull-7 {
  right: 58.33333333%;
}
.egg-container .col-xs-pull-6 {
  right: 50%;
}
.egg-container .col-xs-pull-5 {
  right: 41.66666667%;
}
.egg-container .col-xs-pull-4 {
  right: 33.33333333%;
}
.egg-container .col-xs-pull-3 {
  right: 25%;
}
.egg-container .col-xs-pull-2 {
  right: 16.66666667%;
}
.egg-container .col-xs-pull-1 {
  right: 8.33333333%;
}
.egg-container .col-xs-pull-0 {
  right: auto;
}
.egg-container .col-xs-push-12 {
  left: 100%;
}
.egg-container .col-xs-push-11 {
  left: 91.66666667%;
}
.egg-container .col-xs-push-10 {
  left: 83.33333333%;
}
.egg-container .col-xs-push-9 {
  left: 75%;
}
.egg-container .col-xs-push-8 {
  left: 66.66666667%;
}
.egg-container .col-xs-push-7 {
  left: 58.33333333%;
}
.egg-container .col-xs-push-6 {
  left: 50%;
}
.egg-container .col-xs-push-5 {
  left: 41.66666667%;
}
.egg-container .col-xs-push-4 {
  left: 33.33333333%;
}
.egg-container .col-xs-push-3 {
  left: 25%;
}
.egg-container .col-xs-push-2 {
  left: 16.66666667%;
}
.egg-container .col-xs-push-1 {
  left: 8.33333333%;
}
.egg-container .col-xs-push-0 {
  left: auto;
}
.egg-container .col-xs-offset-12 {
  margin-left: 100%;
}
.egg-container .col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.egg-container .col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.egg-container .col-xs-offset-9 {
  margin-left: 75%;
}
.egg-container .col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.egg-container .col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.egg-container .col-xs-offset-6 {
  margin-left: 50%;
}
.egg-container .col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.egg-container .col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.egg-container .col-xs-offset-3 {
  margin-left: 25%;
}
.egg-container .col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.egg-container .col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.egg-container .col-xs-offset-0 {
  margin-left: 0;
}
@media (min-width: 768px) {
  .egg-container .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left;
  }
  .egg-container .col-sm-12 {
    width: 100%;
  }
  .egg-container .col-sm-11 {
    width: 91.66666667%;
  }
  .egg-container .col-sm-10 {
    width: 83.33333333%;
  }
  .egg-container .col-sm-9 {
    width: 75%;
  }
  .egg-container .col-sm-8 {
    width: 66.66666667%;
  }
  .egg-container .col-sm-7 {
    width: 58.33333333%;
  }
  .egg-container .col-sm-6 {
    width: 50%;
  }
  .egg-container .col-sm-5 {
    width: 41.66666667%;
  }
  .egg-container .col-sm-4 {
    width: 33.33333333%;
  }
  .egg-container .col-sm-3 {
    width: 25%;
  }
  .egg-container .col-sm-2 {
    width: 16.66666667%;
  }
  .egg-container .col-sm-1 {
    width: 8.33333333%;
  }
  .egg-container .col-sm-pull-12 {
    right: 100%;
  }
  .egg-container .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .egg-container .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .egg-container .col-sm-pull-9 {
    right: 75%;
  }
  .egg-container .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .egg-container .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .egg-container .col-sm-pull-6 {
    right: 50%;
  }
  .egg-container .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .egg-container .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .egg-container .col-sm-pull-3 {
    right: 25%;
  }
  .egg-container .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .egg-container .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .egg-container .col-sm-pull-0 {
    right: auto;
  }
  .egg-container .col-sm-push-12 {
    left: 100%;
  }
  .egg-container .col-sm-push-11 {
    left: 91.66666667%;
  }
  .egg-container .col-sm-push-10 {
    left: 83.33333333%;
  }
  .egg-container .col-sm-push-9 {
    left: 75%;
  }
  .egg-container .col-sm-push-8 {
    left: 66.66666667%;
  }
  .egg-container .col-sm-push-7 {
    left: 58.33333333%;
  }
  .egg-container .col-sm-push-6 {
    left: 50%;
  }
  .egg-container .col-sm-push-5 {
    left: 41.66666667%;
  }
  .egg-container .col-sm-push-4 {
    left: 33.33333333%;
  }
  .egg-container .col-sm-push-3 {
    left: 25%;
  }
  .egg-container .col-sm-push-2 {
    left: 16.66666667%;
  }
  .egg-container .col-sm-push-1 {
    left: 8.33333333%;
  }
  .egg-container .col-sm-push-0 {
    left: auto;
  }
  .egg-container .col-sm-offset-12 {
    margin-left: 100%;
  }
  .egg-container .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .egg-container .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .egg-container .col-sm-offset-9 {
    margin-left: 75%;
  }
  .egg-container .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .egg-container .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .egg-container .col-sm-offset-6 {
    margin-left: 50%;
  }
  .egg-container .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .egg-container .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .egg-container .col-sm-offset-3 {
    margin-left: 25%;
  }
  .egg-container .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .egg-container .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .egg-container .col-sm-offset-0 {
    margin-left: 0;
  }
}
@media (min-width: 992px) {
  .egg-container .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left;
  }
  .egg-container .col-md-12 {
    width: 100%;
  }
  .egg-container .col-md-11 {
    width: 91.66666667%;
  }
  .egg-container .col-md-10 {
    width: 83.33333333%;
  }
  .egg-container .col-md-9 {
    width: 75%;
  }
  .egg-container .col-md-8 {
    width: 66.66666667%;
  }
  .egg-container .col-md-7 {
    width: 58.33333333%;
  }
  .egg-container .col-md-6 {
    width: 50%;
  }
  .egg-container .col-md-5 {
    width: 41.66666667%;
  }
  .egg-container .col-md-4 {
    width: 33.33333333%;
  }
  .egg-container .col-md-3 {
    width: 25%;
  }
  .egg-container .col-md-2 {
    width: 16.66666667%;
  }
  .egg-container .col-md-1 {
    width: 8.33333333%;
  }
  .egg-container .col-md-pull-12 {
    right: 100%;
  }
  .egg-container .col-md-pull-11 {
    right: 91.66666667%;
  }
  .egg-container .col-md-pull-10 {
    right: 83.33333333%;
  }
  .egg-container .col-md-pull-9 {
    right: 75%;
  }
  .egg-container .col-md-pull-8 {
    right: 66.66666667%;
  }
  .egg-container .col-md-pull-7 {
    right: 58.33333333%;
  }
  .egg-container .col-md-pull-6 {
    right: 50%;
  }
  .egg-container .col-md-pull-5 {
    right: 41.66666667%;
  }
  .egg-container .col-md-pull-4 {
    right: 33.33333333%;
  }
  .egg-container .col-md-pull-3 {
    right: 25%;
  }
  .egg-container .col-md-pull-2 {
    right: 16.66666667%;
  }
  .egg-container .col-md-pull-1 {
    right: 8.33333333%;
  }
  .egg-container .col-md-pull-0 {
    right: auto;
  }
  .egg-container .col-md-push-12 {
    left: 100%;
  }
  .egg-container .col-md-push-11 {
    left: 91.66666667%;
  }
  .egg-container .col-md-push-10 {
    left: 83.33333333%;
  }
  .egg-container .col-md-push-9 {
    left: 75%;
  }
  .egg-container .col-md-push-8 {
    left: 66.66666667%;
  }
  .egg-container .col-md-push-7 {
    left: 58.33333333%;
  }
  .egg-container .col-md-push-6 {
    left: 50%;
  }
  .egg-container .col-md-push-5 {
    left: 41.66666667%;
  }
  .egg-container .col-md-push-4 {
    left: 33.33333333%;
  }
  .egg-container .col-md-push-3 {
    left: 25%;
  }
  .egg-container .col-md-push-2 {
    left: 16.66666667%;
  }
  .egg-container .col-md-push-1 {
    left: 8.33333333%;
  }
  .egg-container .col-md-push-0 {
    left: auto;
  }
  .egg-container .col-md-offset-12 {
    margin-left: 100%;
  }
  .egg-container .col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .egg-container .col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .egg-container .col-md-offset-9 {
    margin-left: 75%;
  }
  .egg-container .col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .egg-container .col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .egg-container .col-md-offset-6 {
    margin-left: 50%;
  }
  .egg-container .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .egg-container .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .egg-container .col-md-offset-3 {
    margin-left: 25%;
  }
  .egg-container .col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .egg-container .col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .egg-container .col-md-offset-0 {
    margin-left: 0;
  }
}
@media (min-width: 1200px) {
  .egg-container .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left;
  }
  .egg-container .col-lg-12 {
    width: 100%;
  }
  .egg-container .col-lg-11 {
    width: 91.66666667%;
  }
  .egg-container .col-lg-10 {
    width: 83.33333333%;
  }
  .egg-container .col-lg-9 {
    width: 75%;
  }
  .egg-container .col-lg-8 {
    width: 66.66666667%;
  }
  .egg-container .col-lg-7 {
    width: 58.33333333%;
  }
  .egg-container .col-lg-6 {
    width: 50%;
  }
  .egg-container .col-lg-5 {
    width: 41.66666667%;
  }
  .egg-container .col-lg-4 {
    width: 33.33333333%;
  }
  .egg-container .col-lg-3 {
    width: 25%;
  }
  .egg-container .col-lg-2 {
    width: 16.66666667%;
  }
  .egg-container .col-lg-1 {
    width: 8.33333333%;
  }
  .egg-container .col-lg-pull-12 {
    right: 100%;
  }
  .egg-container .col-lg-pull-11 {
    right: 91.66666667%;
  }
  .egg-container .col-lg-pull-10 {
    right: 83.33333333%;
  }
  .egg-container .col-lg-pull-9 {
    right: 75%;
  }
  .egg-container .col-lg-pull-8 {
    right: 66.66666667%;
  }
  .egg-container .col-lg-pull-7 {
    right: 58.33333333%;
  }
  .egg-container .col-lg-pull-6 {
    right: 50%;
  }
  .egg-container .col-lg-pull-5 {
    right: 41.66666667%;
  }
  .egg-container .col-lg-pull-4 {
    right: 33.33333333%;
  }
  .egg-container .col-lg-pull-3 {
    right: 25%;
  }
  .egg-container .col-lg-pull-2 {
    right: 16.66666667%;
  }
  .egg-container .col-lg-pull-1 {
    right: 8.33333333%;
  }
  .egg-container .col-lg-pull-0 {
    right: auto;
  }
  .egg-container .col-lg-push-12 {
    left: 100%;
  }
  .egg-container .col-lg-push-11 {
    left: 91.66666667%;
  }
  .egg-container .col-lg-push-10 {
    left: 83.33333333%;
  }
  .egg-container .col-lg-push-9 {
    left: 75%;
  }
  .egg-container .col-lg-push-8 {
    left: 66.66666667%;
  }
  .egg-container .col-lg-push-7 {
    left: 58.33333333%;
  }
  .egg-container .col-lg-push-6 {
    left: 50%;
  }
  .egg-container .col-lg-push-5 {
    left: 41.66666667%;
  }
  .egg-container .col-lg-push-4 {
    left: 33.33333333%;
  }
  .egg-container .col-lg-push-3 {
    left: 25%;
  }
  .egg-container .col-lg-push-2 {
    left: 16.66666667%;
  }
  .egg-container .col-lg-push-1 {
    left: 8.33333333%;
  }
  .egg-container .col-lg-push-0 {
    left: auto;
  }
  .egg-container .col-lg-offset-12 {
    margin-left: 100%;
  }
  .egg-container .col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .egg-container .col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .egg-container .col-lg-offset-9 {
    margin-left: 75%;
  }
  .egg-container .col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .egg-container .col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .egg-container .col-lg-offset-6 {
    margin-left: 50%;
  }
  .egg-container .col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .egg-container .col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .egg-container .col-lg-offset-3 {
    margin-left: 25%;
  }
  .egg-container .col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .egg-container .col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .egg-container .col-lg-offset-0 {
    margin-left: 0;
  }
}
.egg-container table {
  background-color: transparent;
}
.egg-container caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #777;
  text-align: left;
}
.egg-container th {
  text-align: left;
}
.egg-container .table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.egg-container .table > thead > tr > th,
.egg-container .table > tbody > tr > th,
.egg-container .table > tfoot > tr > th,
.egg-container .table > thead > tr > td,
.egg-container .table > tbody > tr > td,
.egg-container .table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd;
}
.egg-container .table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ddd;
}
.egg-container .table > caption + thead > tr:first-child > th,
.egg-container .table > colgroup + thead > tr:first-child > th,
.egg-container .table > thead:first-child > tr:first-child > th,
.egg-container .table > caption + thead > tr:first-child > td,
.egg-container .table > colgroup + thead > tr:first-child > td,
.egg-container .table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.egg-container .table > tbody + tbody {
  border-top: 2px solid #ddd;
}
.egg-container .table .table {
  background-color: #fff;
}
.egg-container .table-condensed > thead > tr > th,
.egg-container .table-condensed > tbody > tr > th,
.egg-container .table-condensed > tfoot > tr > th,
.egg-container .table-condensed > thead > tr > td,
.egg-container .table-condensed > tbody > tr > td,
.egg-container .table-condensed > tfoot > tr > td {
  padding: 5px;
}
.egg-container .table-bordered {
  border: 1px solid #ddd;
}
.egg-container .table-bordered > thead > tr > th,
.egg-container .table-bordered > tbody > tr > th,
.egg-container .table-bordered > tfoot > tr > th,
.egg-container .table-bordered > thead > tr > td,
.egg-container .table-bordered > tbody > tr > td,
.egg-container .table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}
.egg-container .table-bordered > thead > tr > th,
.egg-container .table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.egg-container .table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
.egg-container .table-hover > tbody > tr:hover {
  background-color: #f5f5f5;
}
.egg-container table col[class*="col-"] {
  position: static;
  display: table-column;
  float: none;
}
.egg-container table td[class*="col-"],
.egg-container table th[class*="col-"] {
  position: static;
  display: table-cell;
  float: none;
}
.egg-container .table > thead > tr > td.active,
.egg-container .table > tbody > tr > td.active,
.egg-container .table > tfoot > tr > td.active,
.egg-container .table > thead > tr > th.active,
.egg-container .table > tbody > tr > th.active,
.egg-container .table > tfoot > tr > th.active,
.egg-container .table > thead > tr.active > td,
.egg-container .table > tbody > tr.active > td,
.egg-container .table > tfoot > tr.active > td,
.egg-container .table > thead > tr.active > th,
.egg-container .table > tbody > tr.active > th,
.egg-container .table > tfoot > tr.active > th {
  background-color: #f5f5f5;
}
.egg-container .table-hover > tbody > tr > td.active:hover,
.egg-container .table-hover > tbody > tr > th.active:hover,
.egg-container .table-hover > tbody > tr.active:hover > td,
.egg-container .table-hover > tbody > tr:hover > .active,
.egg-container .table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8;
}
.egg-container .table > thead > tr > td.success,
.egg-container .table > tbody > tr > td.success,
.egg-container .table > tfoot > tr > td.success,
.egg-container .table > thead > tr > th.success,
.egg-container .table > tbody > tr > th.success,
.egg-container .table > tfoot > tr > th.success,
.egg-container .table > thead > tr.success > td,
.egg-container .table > tbody > tr.success > td,
.egg-container .table > tfoot > tr.success > td,
.egg-container .table > thead > tr.success > th,
.egg-container .table > tbody > tr.success > th,
.egg-container .table > tfoot > tr.success > th {
  background-color: #dff0d8;
}
.egg-container .table-hover > tbody > tr > td.success:hover,
.egg-container .table-hover > tbody > tr > th.success:hover,
.egg-container .table-hover > tbody > tr.success:hover > td,
.egg-container .table-hover > tbody > tr:hover > .success,
.egg-container .table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6;
}
.egg-container .table > thead > tr > td.info,
.egg-container .table > tbody > tr > td.info,
.egg-container .table > tfoot > tr > td.info,
.egg-container .table > thead > tr > th.info,
.egg-container .table > tbody > tr > th.info,
.egg-container .table > tfoot > tr > th.info,
.egg-container .table > thead > tr.info > td,
.egg-container .table > tbody > tr.info > td,
.egg-container .table > tfoot > tr.info > td,
.egg-container .table > thead > tr.info > th,
.egg-container .table > tbody > tr.info > th,
.egg-container .table > tfoot > tr.info > th {
  background-color: #d9edf7;
}
.egg-container .table-hover > tbody > tr > td.info:hover,
.egg-container .table-hover > tbody > tr > th.info:hover,
.egg-container .table-hover > tbody > tr.info:hover > td,
.egg-container .table-hover > tbody > tr:hover > .info,
.egg-container .table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3;
}
.egg-container .table > thead > tr > td.warning,
.egg-container .table > tbody > tr > td.warning,
.egg-container .table > tfoot > tr > td.warning,
.egg-container .table > thead > tr > th.warning,
.egg-container .table > tbody > tr > th.warning,
.egg-container .table > tfoot > tr > th.warning,
.egg-container .table > thead > tr.warning > td,
.egg-container .table > tbody > tr.warning > td,
.egg-container .table > tfoot > tr.warning > td,
.egg-container .table > thead > tr.warning > th,
.egg-container .table > tbody > tr.warning > th,
.egg-container .table > tfoot > tr.warning > th {
  background-color: #fcf8e3;
}
.egg-container .table-hover > tbody > tr > td.warning:hover,
.egg-container .table-hover > tbody > tr > th.warning:hover,
.egg-container .table-hover > tbody > tr.warning:hover > td,
.egg-container .table-hover > tbody > tr:hover > .warning,
.egg-container .table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc;
}
.egg-container .table > thead > tr > td.danger,
.egg-container .table > tbody > tr > td.danger,
.egg-container .table > tfoot > tr > td.danger,
.egg-container .table > thead > tr > th.danger,
.egg-container .table > tbody > tr > th.danger,
.egg-container .table > tfoot > tr > th.danger,
.egg-container .table > thead > tr.danger > td,
.egg-container .table > tbody > tr.danger > td,
.egg-container .table > tfoot > tr.danger > td,
.egg-container .table > thead > tr.danger > th,
.egg-container .table > tbody > tr.danger > th,
.egg-container .table > tfoot > tr.danger > th {
  background-color: #f2dede;
}
.egg-container .table-hover > tbody > tr > td.danger:hover,
.egg-container .table-hover > tbody > tr > th.danger:hover,
.egg-container .table-hover > tbody > tr.danger:hover > td,
.egg-container .table-hover > tbody > tr:hover > .danger,
.egg-container .table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc;
}
.egg-container .table-responsive {
  min-height: .01%;
  overflow-x: auto;
}
@media screen and (max-width: 767px) {
  .egg-container .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ddd;
  }
  .egg-container .table-responsive > .table {
    margin-bottom: 0;
  }
  .egg-container .table-responsive > .table > thead > tr > th,
  .egg-container .table-responsive > .table > tbody > tr > th,
  .egg-container .table-responsive > .table > tfoot > tr > th,
  .egg-container .table-responsive > .table > thead > tr > td,
  .egg-container .table-responsive > .table > tbody > tr > td,
  .egg-container .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .egg-container .table-responsive > .table-bordered {
    border: 0;
  }
  .egg-container .table-responsive > .table-bordered > thead > tr > th:first-child,
  .egg-container .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .egg-container .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .egg-container .table-responsive > .table-bordered > thead > tr > td:first-child,
  .egg-container .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .egg-container .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .egg-container .table-responsive > .table-bordered > thead > tr > th:last-child,
  .egg-container .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .egg-container .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .egg-container .table-responsive > .table-bordered > thead > tr > td:last-child,
  .egg-container .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .egg-container .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .egg-container .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .egg-container .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .egg-container .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .egg-container .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
.egg-container fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
.egg-container legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
.egg-container label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold;
}
.egg-container input[type="search"] {
  -webkit-box-sizing: border-box;
     -moz-box-sizing: border-box;
          box-sizing: border-box;
}
.egg-container input[type="radio"],
.egg-container input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal;
}
.egg-container input[type="file"] {
  display: block;
}
.egg-container input[type="range"] {
  display: block;
  width: 100%;
}
.egg-container select[multiple],
.egg-container select[size] {
  height: auto;
}
.egg-container input[type="file"]:focus,
.egg-container input[type="radio"]:focus,
.egg-container input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.egg-container output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
}
.egg-container .form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
       -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
          transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.egg-container .form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
          box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, .6);
}
.egg-container .form-control::-moz-placeholder {
  color: #999;
  opacity: 1;
}
.egg-container .form-control:-ms-input-placeholder {
  color: #999;
}
.egg-container .form-control::-webkit-input-placeholder {
  color: #999;
}
.egg-container .form-control[disabled],
.egg-container .form-control[readonly],
fieldset[disabled] .egg-container .form-control {
  background-color: #eee;
  opacity: 1;
}
.egg-container .form-control[disabled],
fieldset[disabled] .egg-container .form-control {
  cursor: not-allowed;
}
textarea.egg-container .form-control {
  height: auto;
}
.egg-container input[type="search"] {
  -webkit-appearance: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .egg-container input[type="date"],
  .egg-container input[type="time"],
  .egg-container input[type="datetime-local"],
  .egg-container input[type="month"] {
    line-height: 34px;
  }
  .egg-container input[type="date"].input-sm,
  .egg-container input[type="time"].input-sm,
  .egg-container input[type="datetime-local"].input-sm,
  .egg-container input[type="month"].input-sm,
  .input-group-sm .egg-container input[type="date"],
  .input-group-sm .egg-container input[type="time"],
  .input-group-sm .egg-container input[type="datetime-local"],
  .input-group-sm .egg-container input[type="month"] {
    line-height: 30px;
  }
  .egg-container input[type="date"].input-lg,
  .egg-container input[type="time"].input-lg,
  .egg-container input[type="datetime-local"].input-lg,
  .egg-container input[type="month"].input-lg,
  .input-group-lg .egg-container input[type="date"],
  .input-group-lg .egg-container input[type="time"],
  .input-group-lg .egg-container input[type="datetime-local"],
  .input-group-lg .egg-container input[type="month"] {
    line-height: 46px;
  }
}
.egg-container .form-group {
  margin-bottom: 15px;
}
.egg-container .radio,
.egg-container .checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.egg-container .radio label,
.egg-container .checkbox label {
  min-height: 20px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.egg-container .radio input[type="radio"],
.egg-container .radio-inline input[type="radio"],
.egg-container .checkbox input[type="checkbox"],
.egg-container .checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-top: 4px \9;
  margin-left: -20px;
}
.egg-container .radio + .radio,
.egg-container .checkbox + .checkbox {
  margin-top: -5px;
}
.egg-container .radio-inline,
.egg-container .checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  vertical-align: middle;
  cursor: pointer;
}
.egg-container .radio-inline + .radio-inline,
.egg-container .checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
.egg-container input[type="radio"][disabled],
.egg-container input[type="checkbox"][disabled],
.egg-container input[type="radio"].disabled,
.egg-container input[type="checkbox"].disabled,
fieldset[disabled] .egg-container input[type="radio"],
fieldset[disabled] .egg-container input[type="checkbox"] {
  cursor: not-allowed;
}
.egg-container .radio-inline.disabled,
.egg-container .checkbox-inline.disabled,
fieldset[disabled] .egg-container .radio-inline,
fieldset[disabled] .egg-container .checkbox-inline {
  cursor: not-allowed;
}
.egg-container .radio.disabled label,
.egg-container .checkbox.disabled label,
fieldset[disabled] .egg-container .radio label,
fieldset[disabled] .egg-container .checkbox label {
  cursor: not-allowed;
}
.egg-container .form-control-static {
  min-height: 34px;
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
}
.egg-container .form-control-static.input-lg,
.egg-container .form-control-static.input-sm {
  padding-right: 0;
  padding-left: 0;
}
.egg-container .input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.egg-container .input-sm {
  height: 30px;
  line-height: 30px;
}
textarea.egg-container .input-sm,
select[multiple].egg-container .input-sm {
  height: auto;
}
.egg-container .form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.egg-container .form-group-sm .form-control {
  height: 30px;
  line-height: 30px;
}
textarea.egg-container .form-group-sm .form-control,
select[multiple].egg-container .form-group-sm .form-control {
  height: auto;
}
.egg-container .form-group-sm .form-control-static {
  height: 30px;
  min-height: 32px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
}
.egg-container .input-lg {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.egg-container .input-lg {
  height: 46px;
  line-height: 46px;
}
textarea.egg-container .input-lg,
select[multiple].egg-container .input-lg {
  height: auto;
}
.egg-container .form-group-lg .form-control {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.egg-container .form-group-lg .form-control {
  height: 46px;
  line-height: 46px;
}
textarea.egg-container .form-group-lg .form-control,
select[multiple].egg-container .form-group-lg .form-control {
  height: auto;
}
.egg-container .form-group-lg .form-control-static {
  height: 46px;
  min-height: 38px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
}
.egg-container .has-feedback {
  position: relative;
}
.egg-container .has-feedback .form-control {
  padding-right: 42.5px;
}
.egg-container .form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none;
}
.egg-container .input-lg + .form-control-feedback {
  width: 46px;
  height: 46px;
  line-height: 46px;
}
.egg-container .input-sm + .form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px;
}
.egg-container .has-success .help-block,
.egg-container .has-success .control-label,
.egg-container .has-success .radio,
.egg-container .has-success .checkbox,
.egg-container .has-success .radio-inline,
.egg-container .has-success .checkbox-inline,
.egg-container .has-success.radio label,
.egg-container .has-success.checkbox label,
.egg-container .has-success.radio-inline label,
.egg-container .has-success.checkbox-inline label {
  color: #3c763d;
}
.egg-container .has-success .form-control {
  border-color: #3c763d;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.egg-container .has-success .form-control:focus {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #67b168;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #67b168;
}
.egg-container .has-success .input-group-addon {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #3c763d;
}
.egg-container .has-success .form-control-feedback {
  color: #3c763d;
}
.egg-container .has-warning .help-block,
.egg-container .has-warning .control-label,
.egg-container .has-warning .radio,
.egg-container .has-warning .checkbox,
.egg-container .has-warning .radio-inline,
.egg-container .has-warning .checkbox-inline,
.egg-container .has-warning.radio label,
.egg-container .has-warning.checkbox label,
.egg-container .has-warning.radio-inline label,
.egg-container .has-warning.checkbox-inline label {
  color: #8a6d3b;
}
.egg-container .has-warning .form-control {
  border-color: #8a6d3b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.egg-container .has-warning .form-control:focus {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #c0a16b;
}
.egg-container .has-warning .input-group-addon {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #8a6d3b;
}
.egg-container .has-warning .form-control-feedback {
  color: #8a6d3b;
}
.egg-container .has-error .help-block,
.egg-container .has-error .control-label,
.egg-container .has-error .radio,
.egg-container .has-error .checkbox,
.egg-container .has-error .radio-inline,
.egg-container .has-error .checkbox-inline,
.egg-container .has-error.radio label,
.egg-container .has-error.checkbox label,
.egg-container .has-error.radio-inline label,
.egg-container .has-error.checkbox-inline label {
  color: #a94442;
}
.egg-container .has-error .form-control {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
}
.egg-container .has-error .form-control:focus {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483;
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 6px #ce8483;
}
.egg-container .has-error .input-group-addon {
  color: #a94442;
  background-color: #f2dede;
  border-color: #a94442;
}
.egg-container .has-error .form-control-feedback {
  color: #a94442;
}
.egg-container .has-feedback label ~ .form-control-feedback {
  top: 25px;
}
.egg-container .has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.egg-container .help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}
@media (min-width: 768px) {
  .egg-container .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .egg-container .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .egg-container .form-inline .form-control-static {
    display: inline-block;
  }
  .egg-container .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .egg-container .form-inline .input-group .input-group-addon,
  .egg-container .form-inline .input-group .input-group-btn,
  .egg-container .form-inline .input-group .form-control {
    width: auto;
  }
  .egg-container .form-inline .input-group > .form-control {
    width: 100%;
  }
  .egg-container .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .egg-container .form-inline .radio,
  .egg-container .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .egg-container .form-inline .radio label,
  .egg-container .form-inline .checkbox label {
    padding-left: 0;
  }
  .egg-container .form-inline .radio input[type="radio"],
  .egg-container .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .egg-container .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.egg-container .form-horizontal .radio,
.egg-container .form-horizontal .checkbox,
.egg-container .form-horizontal .radio-inline,
.egg-container .form-horizontal .checkbox-inline {
  padding-top: 7px;
  margin-top: 0;
  margin-bottom: 0;
}
.egg-container .form-horizontal .radio,
.egg-container .form-horizontal .checkbox {
  min-height: 27px;
}
.egg-container .form-horizontal .form-group {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .egg-container .form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
  }
}
.egg-container .form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 768px) {
  .egg-container .form-horizontal .form-group-lg .control-label {
    padding-top: 14.333333px;
  }
}
@media (min-width: 768px) {
  .egg-container .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
  }
}
.egg-container .btn {
  display: inline-block;
  padding: 7px 14px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
      touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
.egg-container .btn:focus,
.egg-container .btn:active:focus,
.egg-container .btn.active:focus,
.egg-container .btn.focus,
.egg-container .btn:active.focus,
.egg-container .btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.egg-container .btn:hover,
.egg-container .btn:focus,
.egg-container .btn.focus {
  color: #333;
  text-decoration: none;
}
.egg-container .btn:active,
.egg-container .btn.active {
  background-image: none;
  outline: 0;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.egg-container .btn.disabled,
.egg-container .btn[disabled],
fieldset[disabled] .egg-container .btn {
  pointer-events: none;
  cursor: not-allowed;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
          box-shadow: none;
  opacity: .65;
}
.egg-container .btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}
.egg-container .btn-default:hover,
.egg-container .btn-default:focus,
.egg-container .btn-default.focus,
.egg-container .btn-default:active,
.egg-container .btn-default.active,
.open > .dropdown-toggle.egg-container .btn-default {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.egg-container .btn-default:active,
.egg-container .btn-default.active,
.open > .dropdown-toggle.egg-container .btn-default {
  background-image: none;
}
.egg-container .btn-default.disabled,
.egg-container .btn-default[disabled],
fieldset[disabled] .egg-container .btn-default,
.egg-container .btn-default.disabled:hover,
.egg-container .btn-default[disabled]:hover,
fieldset[disabled] .egg-container .btn-default:hover,
.egg-container .btn-default.disabled:focus,
.egg-container .btn-default[disabled]:focus,
fieldset[disabled] .egg-container .btn-default:focus,
.egg-container .btn-default.disabled.focus,
.egg-container .btn-default[disabled].focus,
fieldset[disabled] .egg-container .btn-default.focus,
.egg-container .btn-default.disabled:active,
.egg-container .btn-default[disabled]:active,
fieldset[disabled] .egg-container .btn-default:active,
.egg-container .btn-default.disabled.active,
.egg-container .btn-default[disabled].active,
fieldset[disabled] .egg-container .btn-default.active {
  background-color: #fff;
  border-color: #ccc;
}
.egg-container .btn-default .badge {
  color: #fff;
  background-color: #333;
}
.egg-container .btn-primary {
  color: #fff;
  background-color: #337ab7;
  border-color: #2e6da4;
}
.egg-container .btn-primary:hover,
.egg-container .btn-primary:focus,
.egg-container .btn-primary.focus,
.egg-container .btn-primary:active,
.egg-container .btn-primary.active,
.open > .dropdown-toggle.egg-container .btn-primary {
  color: #fff;
  background-color: #286090;
  border-color: #204d74;
}
.egg-container .btn-primary:active,
.egg-container .btn-primary.active,
.open > .dropdown-toggle.egg-container .btn-primary {
  background-image: none;
}
.egg-container .btn-primary.disabled,
.egg-container .btn-primary[disabled],
fieldset[disabled] .egg-container .btn-primary,
.egg-container .btn-primary.disabled:hover,
.egg-container .btn-primary[disabled]:hover,
fieldset[disabled] .egg-container .btn-primary:hover,
.egg-container .btn-primary.disabled:focus,
.egg-container .btn-primary[disabled]:focus,
fieldset[disabled] .egg-container .btn-primary:focus,
.egg-container .btn-primary.disabled.focus,
.egg-container .btn-primary[disabled].focus,
fieldset[disabled] .egg-container .btn-primary.focus,
.egg-container .btn-primary.disabled:active,
.egg-container .btn-primary[disabled]:active,
fieldset[disabled] .egg-container .btn-primary:active,
.egg-container .btn-primary.disabled.active,
.egg-container .btn-primary[disabled].active,
fieldset[disabled] .egg-container .btn-primary.active {
  background-color: #337ab7;
  border-color: #2e6da4;
}
.egg-container .btn-primary .badge {
  color: #337ab7;
  background-color: #fff;
}
.egg-container .btn-success {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c;
  text-decoration: none !important;
}
.egg-container .btn-success:hover,
.egg-container .btn-success:focus,
.egg-container .btn-success.focus,
.egg-container .btn-success:active,
.egg-container .btn-success.active,
.open > .dropdown-toggle.egg-container .btn-success {
  color: #fff;
  background-color: #449d44;
  border-color: #398439;
}
.egg-container .btn-success:active,
.egg-container .btn-success.active,
.open > .dropdown-toggle.egg-container .btn-success {
  background-image: none;
}
.egg-container .btn-success.disabled,
.egg-container .btn-success[disabled],
fieldset[disabled] .egg-container .btn-success,
.egg-container .btn-success.disabled:hover,
.egg-container .btn-success[disabled]:hover,
fieldset[disabled] .egg-container .btn-success:hover,
.egg-container .btn-success.disabled:focus,
.egg-container .btn-success[disabled]:focus,
fieldset[disabled] .egg-container .btn-success:focus,
.egg-container .btn-success.disabled.focus,
.egg-container .btn-success[disabled].focus,
fieldset[disabled] .egg-container .btn-success.focus,
.egg-container .btn-success.disabled:active,
.egg-container .btn-success[disabled]:active,
fieldset[disabled] .egg-container .btn-success:active,
.egg-container .btn-success.disabled.active,
.egg-container .btn-success[disabled].active,
fieldset[disabled] .egg-container .btn-success.active {
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.egg-container .btn-success .badge {
  color: #5cb85c;
  background-color: #fff;
}
.egg-container .btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.egg-container .btn-info:hover,
.egg-container .btn-info:focus,
.egg-container .btn-info.focus,
.egg-container .btn-info:active,
.egg-container .btn-info.active,
.open > .dropdown-toggle.egg-container .btn-info {
  color: #fff;
  background-color: #31b0d5;
  border-color: #269abc;
}
.egg-container .btn-info:active,
.egg-container .btn-info.active,
.open > .dropdown-toggle.egg-container .btn-info {
  background-image: none;
}
.egg-container .btn-info.disabled,
.egg-container .btn-info[disabled],
fieldset[disabled] .egg-container .btn-info,
.egg-container .btn-info.disabled:hover,
.egg-container .btn-info[disabled]:hover,
fieldset[disabled] .egg-container .btn-info:hover,
.egg-container .btn-info.disabled:focus,
.egg-container .btn-info[disabled]:focus,
fieldset[disabled] .egg-container .btn-info:focus,
.egg-container .btn-info.disabled.focus,
.egg-container .btn-info[disabled].focus,
fieldset[disabled] .egg-container .btn-info.focus,
.egg-container .btn-info.disabled:active,
.egg-container .btn-info[disabled]:active,
fieldset[disabled] .egg-container .btn-info:active,
.egg-container .btn-info.disabled.active,
.egg-container .btn-info[disabled].active,
fieldset[disabled] .egg-container .btn-info.active {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.egg-container .btn-info .badge {
  color: #5bc0de;
  background-color: #fff;
}
.egg-container .btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.egg-container .btn-warning:hover,
.egg-container .btn-warning:focus,
.egg-container .btn-warning.focus,
.egg-container .btn-warning:active,
.egg-container .btn-warning.active,
.open > .dropdown-toggle.egg-container .btn-warning {
  color: #fff;
  background-color: #ec971f;
  border-color: #d58512;
}
.egg-container .btn-warning:active,
.egg-container .btn-warning.active,
.open > .dropdown-toggle.egg-container .btn-warning {
  background-image: none;
}
.egg-container .btn-warning.disabled,
.egg-container .btn-warning[disabled],
fieldset[disabled] .egg-container .btn-warning,
.egg-container .btn-warning.disabled:hover,
.egg-container .btn-warning[disabled]:hover,
fieldset[disabled] .egg-container .btn-warning:hover,
.egg-container .btn-warning.disabled:focus,
.egg-container .btn-warning[disabled]:focus,
fieldset[disabled] .egg-container .btn-warning:focus,
.egg-container .btn-warning.disabled.focus,
.egg-container .btn-warning[disabled].focus,
fieldset[disabled] .egg-container .btn-warning.focus,
.egg-container .btn-warning.disabled:active,
.egg-container .btn-warning[disabled]:active,
fieldset[disabled] .egg-container .btn-warning:active,
.egg-container .btn-warning.disabled.active,
.egg-container .btn-warning[disabled].active,
fieldset[disabled] .egg-container .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.egg-container .btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff;
}
.egg-container .btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
.egg-container .btn-danger:hover,
.egg-container .btn-danger:focus,
.egg-container .btn-danger.focus,
.egg-container .btn-danger:active,
.egg-container .btn-danger.active,
.open > .dropdown-toggle.egg-container .btn-danger {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}
.egg-container .btn-danger:active,
.egg-container .btn-danger.active,
.open > .dropdown-toggle.egg-container .btn-danger {
  background-image: none;
}
.egg-container .btn-danger.disabled,
.egg-container .btn-danger[disabled],
fieldset[disabled] .egg-container .btn-danger,
.egg-container .btn-danger.disabled:hover,
.egg-container .btn-danger[disabled]:hover,
fieldset[disabled] .egg-container .btn-danger:hover,
.egg-container .btn-danger.disabled:focus,
.egg-container .btn-danger[disabled]:focus,
fieldset[disabled] .egg-container .btn-danger:focus,
.egg-container .btn-danger.disabled.focus,
.egg-container .btn-danger[disabled].focus,
fieldset[disabled] .egg-container .btn-danger.focus,
.egg-container .btn-danger.disabled:active,
.egg-container .btn-danger[disabled]:active,
fieldset[disabled] .egg-container .btn-danger:active,
.egg-container .btn-danger.disabled.active,
.egg-container .btn-danger[disabled].active,
fieldset[disabled] .egg-container .btn-danger.active {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.egg-container .btn-danger .badge {
  color: #d9534f;
  background-color: #fff;
}
.egg-container .btn-link {
  font-weight: normal;
  color: #337ab7;
  border-radius: 0;
}
.egg-container .btn-link,
.egg-container .btn-link:active,
.egg-container .btn-link.active,
.egg-container .btn-link[disabled],
fieldset[disabled] .egg-container .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.egg-container .btn-link,
.egg-container .btn-link:hover,
.egg-container .btn-link:focus,
.egg-container .btn-link:active {
  border-color: transparent;
}
.egg-container .btn-link:hover,
.egg-container .btn-link:focus {
  color: #23527c;
  text-decoration: underline;
  background-color: transparent;
}
.egg-container .btn-link[disabled]:hover,
fieldset[disabled] .egg-container .btn-link:hover,
.egg-container .btn-link[disabled]:focus,
fieldset[disabled] .egg-container .btn-link:focus {
  color: #777;
  text-decoration: none;
}
.egg-container .btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.egg-container .btn-sm {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.egg-container .btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.egg-container .btn-block {
  display: block;
  width: 100%;
}
.egg-container .btn-block + .btn-block {
  margin-top: 5px;
}
.egg-container input[type="submit"].btn-block,
.egg-container input[type="reset"].btn-block,
.egg-container input[type="button"].btn-block {
  width: 100%;
}
.egg-container .fade {
  opacity: 0;
  -webkit-transition: opacity .15s linear;
       -o-transition: opacity .15s linear;
          transition: opacity .15s linear;
}
.egg-container .fade.in {
  opacity: 1;
}
.egg-container .collapse {
  display: none;
}
.egg-container .collapse.in {
  display: block;
}
tr.egg-container .collapse.in {
  display: table-row;
}
tbody.egg-container .collapse.in {
  display: table-row-group;
}
.egg-container .collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-timing-function: ease;
       -o-transition-timing-function: ease;
          transition-timing-function: ease;
  -webkit-transition-duration: .35s;
       -o-transition-duration: .35s;
          transition-duration: .35s;
  -webkit-transition-property: height, visibility;
       -o-transition-property: height, visibility;
          transition-property: height, visibility;
}
.egg-container .caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.egg-container .dropup,
.egg-container .dropdown {
  position: relative;
}
.egg-container .dropdown-toggle:focus {
  outline: 0;
}
.egg-container .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
          background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
          box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}
.egg-container .dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.egg-container .dropdown-menu .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.egg-container .dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}
.egg-container .dropdown-menu > li > a:hover,
.egg-container .dropdown-menu > li > a:focus {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5;
}
.egg-container .dropdown-menu > .active > a,
.egg-container .dropdown-menu > .active > a:hover,
.egg-container .dropdown-menu > .active > a:focus {
  color: #fff;
  text-decoration: none;
  background-color: #337ab7;
  outline: 0;
}
.egg-container .dropdown-menu > .disabled > a,
.egg-container .dropdown-menu > .disabled > a:hover,
.egg-container .dropdown-menu > .disabled > a:focus {
  color: #777;
}
.egg-container .dropdown-menu > .disabled > a:hover,
.egg-container .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.egg-container .open > .dropdown-menu {
  display: block;
}
.egg-container .open > a {
  outline: 0;
}
.egg-container .dropdown-menu-right {
  right: 0;
  left: auto;
}
.egg-container .dropdown-menu-left {
  right: auto;
  left: 0;
}
.egg-container .dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #777;
  white-space: nowrap;
}
.egg-container .dropdown-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 990;
}
.egg-container .pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.egg-container .dropup .caret,
.egg-container .navbar-fixed-bottom .dropdown .caret {
  content: "";
  border-top: 0;
  border-bottom: 4px solid;
}
.egg-container .dropup .dropdown-menu,
.egg-container .navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 768px) {
  .egg-container .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
  .egg-container .navbar-right .dropdown-menu-left {
    right: auto;
    left: 0;
  }
}
.egg-container .btn-group,
.egg-container .btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.egg-container .btn-group > .btn,
.egg-container .btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.egg-container .btn-group > .btn:hover,
.egg-container .btn-group-vertical > .btn:hover,
.egg-container .btn-group > .btn:focus,
.egg-container .btn-group-vertical > .btn:focus,
.egg-container .btn-group > .btn:active,
.egg-container .btn-group-vertical > .btn:active,
.egg-container .btn-group > .btn.active,
.egg-container .btn-group-vertical > .btn.active {
  z-index: 2;
}
.egg-container .btn-group .btn + .btn,
.egg-container .btn-group .btn + .btn-group,
.egg-container .btn-group .btn-group + .btn,
.egg-container .btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.egg-container .btn-toolbar {
  margin-left: -5px;
}
.egg-container .btn-toolbar .btn-group,
.egg-container .btn-toolbar .input-group {
  float: left;
}
.egg-container .btn-toolbar > .btn,
.egg-container .btn-toolbar > .btn-group,
.egg-container .btn-toolbar > .input-group {
  margin-left: 5px;
}
.egg-container .btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.egg-container .btn-group > .btn:first-child {
  margin-left: 0;
}
.egg-container .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.egg-container .btn-group > .btn:last-child:not(:first-child),
.egg-container .btn-group > .dropdown-toggle:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.egg-container .btn-group > .btn-group {
  float: left;
}
.egg-container .btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.egg-container .btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.egg-container .btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.egg-container .btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.egg-container .btn-group .dropdown-toggle:active,
.egg-container .btn-group.open .dropdown-toggle {
  outline: 0;
}
.egg-container .btn-group > .btn + .dropdown-toggle {
  padding-right: 8px;
  padding-left: 8px;
}
.egg-container .btn-group > .btn-lg + .dropdown-toggle {
  padding-right: 12px;
  padding-left: 12px;
}
.egg-container .btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
          box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}
.egg-container .btn-group.open .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
          box-shadow: none;
}
.egg-container .btn .caret {
  margin-left: 0;
}
.egg-container .btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.egg-container .dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.egg-container .btn-group-vertical > .btn,
.egg-container .btn-group-vertical > .btn-group,
.egg-container .btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.egg-container .btn-group-vertical > .btn-group > .btn {
  float: none;
}
.egg-container .btn-group-vertical > .btn + .btn,
.egg-container .btn-group-vertical > .btn + .btn-group,
.egg-container .btn-group-vertical > .btn-group + .btn,
.egg-container .btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.egg-container .btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.egg-container .btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.egg-container .btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 4px;
}
.egg-container .btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.egg-container .btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.egg-container .btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.egg-container .btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.egg-container .btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.egg-container .btn-group-justified > .btn,
.egg-container .btn-group-justified > .btn-group {
  display: table-cell;
  float: none;
  width: 1%;
}
.egg-container .btn-group-justified > .btn-group .btn {
  width: 100%;
}
.egg-container .btn-group-justified > .btn-group .dropdown-menu {
  left: auto;
}
.egg-container [data-toggle="buttons"] > .btn input[type="radio"],
.egg-container [data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
.egg-container [data-toggle="buttons"] > .btn input[type="checkbox"],
.egg-container [data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.egg-container .input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.egg-container .input-group[class*="col-"] {
  float: none;
  padding-right: 0;
  padding-left: 0;
}
.egg-container .input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.egg-container .input-group-lg > .form-control,
.egg-container .input-group-lg > .input-group-addon,
.egg-container .input-group-lg > .input-group-btn > .btn {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.egg-container .input-group-lg > .form-control,
select.egg-container .input-group-lg > .input-group-addon,
select.egg-container .input-group-lg > .input-group-btn > .btn {
  height: 46px;
  line-height: 46px;
}
textarea.egg-container .input-group-lg > .form-control,
textarea.egg-container .input-group-lg > .input-group-addon,
textarea.egg-container .input-group-lg > .input-group-btn > .btn,
select[multiple].egg-container .input-group-lg > .form-control,
select[multiple].egg-container .input-group-lg > .input-group-addon,
select[multiple].egg-container .input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.egg-container .input-group-sm > .form-control,
.egg-container .input-group-sm > .input-group-addon,
.egg-container .input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.egg-container .input-group-sm > .form-control,
select.egg-container .input-group-sm > .input-group-addon,
select.egg-container .input-group-sm > .input-group-btn > .btn {
  height: 30px;
  line-height: 30px;
}
textarea.egg-container .input-group-sm > .form-control,
textarea.egg-container .input-group-sm > .input-group-addon,
textarea.egg-container .input-group-sm > .input-group-btn > .btn,
select[multiple].egg-container .input-group-sm > .form-control,
select[multiple].egg-container .input-group-sm > .input-group-addon,
select[multiple].egg-container .input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.egg-container .input-group-addon,
.egg-container .input-group-btn,
.egg-container .input-group .form-control {
  display: table-cell;
}
.egg-container .input-group-addon:not(:first-child):not(:last-child),
.egg-container .input-group-btn:not(:first-child):not(:last-child),
.egg-container .input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.egg-container .input-group-addon,
.egg-container .input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.egg-container .input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #555;
  text-align: center;
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.egg-container .input-group-addon.input-sm {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 3px;
}
.egg-container .input-group-addon.input-lg {
  padding: 10px 16px;
  font-size: 18px;
  border-radius: 6px;
}
.egg-container .input-group-addon input[type="radio"],
.egg-container .input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.egg-container .input-group .form-control:first-child,
.egg-container .input-group-addon:first-child,
.egg-container .input-group-btn:first-child > .btn,
.egg-container .input-group-btn:first-child > .btn-group > .btn,
.egg-container .input-group-btn:first-child > .dropdown-toggle,
.egg-container .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.egg-container .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.egg-container .input-group-addon:first-child {
  border-right: 0;
}
.egg-container .input-group .form-control:last-child,
.egg-container .input-group-addon:last-child,
.egg-container .input-group-btn:last-child > .btn,
.egg-container .input-group-btn:last-child > .btn-group > .btn,
.egg-container .input-group-btn:last-child > .dropdown-toggle,
.egg-container .input-group-btn:first-child > .btn:not(:first-child),
.egg-container .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.egg-container .input-group-addon:last-child {
  border-left: 0;
}
.egg-container .input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.egg-container .input-group-btn > .btn {
  position: relative;
}
.egg-container .input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.egg-container .input-group-btn > .btn:hover,
.egg-container .input-group-btn > .btn:focus,
.egg-container .input-group-btn > .btn:active {
  z-index: 2;
}
.egg-container .input-group-btn:first-child > .btn,
.egg-container .input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}
.egg-container .input-group-btn:last-child > .btn,
.egg-container .input-group-btn:last-child > .btn-group {
  margin-left: -1px;
}
.egg-container .nav {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.egg-container .nav > li {
  position: relative;
  display: block;
}
.egg-container .nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.egg-container .nav > li > a:hover,
.egg-container .nav > li > a:focus {
  text-decoration: none;
  background-color: #eee;
}
.egg-container .nav > li.disabled > a {
  color: #777;
}
.egg-container .nav > li.disabled > a:hover,
.egg-container .nav > li.disabled > a:focus {
  color: #777;
  text-decoration: none;
  cursor: not-allowed;
  background-color: transparent;
}
.egg-container .nav .open > a,
.egg-container .nav .open > a:hover,
.egg-container .nav .open > a:focus {
  background-color: #eee;
  border-color: #337ab7;
}
.egg-container .nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.egg-container .nav > li > a > img {
  max-width: none;
}
.egg-container .nav-tabs {
  border-bottom: 1px solid #ddd;
}
.egg-container .nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.egg-container .nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.egg-container .nav-tabs > li > a:hover {
  border-color: #eee #eee #ddd;
}
.egg-container .nav-tabs > li.active > a,
.egg-container .nav-tabs > li.active > a:hover,
.egg-container .nav-tabs > li.active > a:focus {
  color: #555;
  cursor: default;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}
.egg-container .nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.egg-container .nav-tabs.nav-justified > li {
  float: none;
}
.egg-container .nav-tabs.nav-justified > li > a {
  margin-bottom: 5px;
  text-align: center;
}
.egg-container .nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .egg-container .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .egg-container .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.egg-container .nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.egg-container .nav-tabs.nav-justified > .active > a,
.egg-container .nav-tabs.nav-justified > .active > a:hover,
.egg-container .nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #ddd;
}
@media (min-width: 768px) {
  .egg-container .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .egg-container .nav-tabs.nav-justified > .active > a,
  .egg-container .nav-tabs.nav-justified > .active > a:hover,
  .egg-container .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
.egg-container .nav-pills > li {
  float: left;
}
.egg-container .nav-pills > li > a {
  border-radius: 4px;
}
.egg-container .nav-pills > li + li {
  margin-left: 2px;
}
.egg-container .nav-pills > li.active > a,
.egg-container .nav-pills > li.active > a:hover,
.egg-container .nav-pills > li.active > a:focus {
  color: #fff;
  background-color: #337ab7;
}
.egg-container .nav-stacked > li {
  float: none;
}
.egg-container .nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.egg-container .nav-justified {
  width: 100%;
}
.egg-container .nav-justified > li {
  float: none;
}
.egg-container .nav-justified > li > a {
  margin-bottom: 5px;
  text-align: center;
}
.egg-container .nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .egg-container .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .egg-container .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.egg-container .nav-tabs-justified {
  border-bottom: 0;
}
.egg-container .nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.egg-container .nav-tabs-justified > .active > a,
.egg-container .nav-tabs-justified > .active > a:hover,
.egg-container .nav-tabs-justified > .active > a:focus {
  border: 1px solid #ddd;
}
@media (min-width: 768px) {
  .egg-container .nav-tabs-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 4px 4px 0 0;
  }
  .egg-container .nav-tabs-justified > .active > a,
  .egg-container .nav-tabs-justified > .active > a:hover,
  .egg-container .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
.egg-container .tab-content > .tab-pane {
  display: none;
}
.egg-container .tab-content > .active {
  display: block;
}
.egg-container .nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.egg-container .breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 4px;
}
.egg-container .breadcrumb > li {
  display: inline-block;
}
.egg-container .breadcrumb > li + li:before {
  padding: 0 5px;
  color: #ccc;
  content: "/\00a0";
}
.egg-container .breadcrumb > .active {
  color: #777;
}
.egg-container .label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}
a.egg-container .label:hover,
a.egg-container .label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.egg-container .label:empty {
  display: none;
}
.btn .egg-container .label {
  position: relative;
  top: -1px;
}
.egg-container .label-default {
  background-color: #777;
}
.egg-container .label-default[href]:hover,
.egg-container .label-default[href]:focus {
  background-color: #5e5e5e;
}
.egg-container .label-primary {
  background-color: #337ab7;
}
.egg-container .label-primary[href]:hover,
.egg-container .label-primary[href]:focus {
  background-color: #286090;
}
.egg-container .label-success {
  background-color: #5cb85c;
}
.egg-container .label-success[href]:hover,
.egg-container .label-success[href]:focus {
  background-color: #449d44;
}
.egg-container .label-info {
  background-color: #5bc0de;
}
.egg-container .label-info[href]:hover,
.egg-container .label-info[href]:focus {
  background-color: #31b0d5;
}
.egg-container .label-warning {
  background-color: #f0ad4e;
}
.egg-container .label-warning[href]:hover,
.egg-container .label-warning[href]:focus {
  background-color: #ec971f;
}
.egg-container .label-danger {
  background-color: #d9534f;
}
.egg-container .label-danger[href]:hover,
.egg-container .label-danger[href]:focus {
  background-color: #c9302c;
}
.egg-container .badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  background-color: #777;
  border-radius: 10px;
}
.egg-container .badge:empty {
  display: none;
}
.btn .egg-container .badge {
  position: relative;
  top: -1px;
}
.btn-xs .egg-container .badge,
.btn-group-xs > .btn .egg-container .badge {
  top: 0;
  padding: 1px 5px;
}
a.egg-container .badge:hover,
a.egg-container .badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.list-group-item.active > .egg-container .badge,
.nav-pills > .active > a > .egg-container .badge {
  color: #337ab7;
  background-color: #fff;
}
.list-group-item > .egg-container .badge {
  float: right;
}
.list-group-item > .egg-container .badge + .egg-container .badge {
  margin-right: 5px;
}
.nav-pills > li > a > .egg-container .badge {
  margin-left: 3px;
}
.egg-container .thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 20px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: border .2s ease-in-out;
       -o-transition: border .2s ease-in-out;
          transition: border .2s ease-in-out;
}
.egg-container .thumbnail > img,
.egg-container .thumbnail a > img {
  margin-right: auto;
  margin-left: auto;
}

.egg-container a.thumbnail:hover,
.egg-container a.thumbnail:focus,
.egg-container a.thumbnail.active {
  border-color: #337ab7;
}

.egg-container .thumbnail .caption {
  padding: 9px;
  color: #333;
}
.egg-container .alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.egg-container .alert h4 {
  margin-top: 0;
  color: inherit;
}
.egg-container .alert .alert-link {
  font-weight: bold;
}
.egg-container .alert > p,
.egg-container .alert > ul {
  margin-bottom: 0;
}
.egg-container .alert > p + p {
  margin-top: 5px;
}
.egg-container .alert-dismissable,
.egg-container .alert-dismissible {
  padding-right: 35px;
}
.egg-container .alert-dismissable .close,
.egg-container .alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.egg-container .alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.egg-container .alert-success hr {
  border-top-color: #c9e2b3;
}
.egg-container .alert-success .alert-link {
  color: #2b542c;
}
.egg-container .alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.egg-container .alert-info hr {
  border-top-color: #a6e1ec;
}
.egg-container .alert-info .alert-link {
  color: #245269;
}
.egg-container .alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.egg-container .alert-warning hr {
  border-top-color: #f7e1b5;
}
.egg-container .alert-warning .alert-link {
  color: #66512c;
}
.egg-container .alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.egg-container .alert-danger hr {
  border-top-color: #e4b9c0;
}
.egg-container .alert-danger .alert-link {
  color: #843534;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-o-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.egg-container .progress {
  height: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1);
}
.egg-container .progress-bar {
  float: left;
  width: 0;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #337ab7;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
          box-shadow: inset 0 -1px 0 rgba(0, 0, 0, .15);
  -webkit-transition: width .6s ease;
       -o-transition: width .6s ease;
          transition: width .6s ease;
}
.egg-container .progress-striped .progress-bar,
.egg-container .progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  -webkit-background-size: 40px 40px;
          background-size: 40px 40px;
}
.egg-container .progress.active .progress-bar,
.egg-container .progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
       -o-animation: progress-bar-stripes 2s linear infinite;
          animation: progress-bar-stripes 2s linear infinite;
}
.egg-container .progress-bar-success {
  background-color: #5cb85c;
}
.progress-striped .egg-container .progress-bar-success {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}
.egg-container .progress-bar-info {
  background-color: #5bc0de;
}
.progress-striped .egg-container .progress-bar-info {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}
.egg-container .progress-bar-warning {
  background-color: #f0ad4e;
}
.progress-striped .egg-container .progress-bar-warning {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}
.egg-container .progress-bar-danger {
  background-color: #d9534f;
}
.progress-striped .egg-container .progress-bar-danger {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:      -o-linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
  background-image:         linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
}
.egg-container .media {
  margin-top: 15px;
}
.egg-container .media:first-child {
  margin-top: 0;
}
.egg-container .media,
.egg-container .media-body {
  overflow: hidden;
  zoom: 1;
}
.egg-container .media-body {
  width: 10000px;
}
.egg-container .media-object {
  display: block;
}
.egg-container .media-right,
.egg-container .media > .pull-right {
  padding-left: 10px;
}
.egg-container .media-left,
.egg-container .media > .pull-left {
  padding-right: 20px;
}
.egg-container .media-left,
.egg-container .media-right,
.egg-container .media-body {
  display: table-cell;
  vertical-align: top;
}
.egg-container .media-middle {
  vertical-align: middle;
}
.egg-container .media-bottom {
  vertical-align: bottom;
}
.egg-container .media-heading {
  margin-top: 0;
  margin-bottom: 18px;
}
.egg-container .media-list {
  padding-left: 0;
  list-style: none;
}
.egg-container .list-group {
  padding-left: 0;
  margin-bottom: 20px;
}
.egg-container .list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.egg-container .list-group-item:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}
.egg-container .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.egg-container a.list-group-item {
  color: #555;
}
.egg-container a.list-group-item .list-group-item-heading {
  color: #333;
}
.egg-container a.list-group-item:hover,
.egg-container a.list-group-item:focus {
  color: #555;
  text-decoration: none;
  background-color: #f5f5f5;
}
.egg-container .list-group-item.disabled,
.egg-container .list-group-item.disabled:hover,
.egg-container .list-group-item.disabled:focus {
  color: #777;
  cursor: not-allowed;
  background-color: #eee;
}
.egg-container .list-group-item.disabled .list-group-item-heading,
.egg-container .list-group-item.disabled:hover .list-group-item-heading,
.egg-container .list-group-item.disabled:focus .list-group-item-heading {
  color: inherit;
}
.egg-container .list-group-item.disabled .list-group-item-text,
.egg-container .list-group-item.disabled:hover .list-group-item-text,
.egg-container .list-group-item.disabled:focus .list-group-item-text {
  color: #777;
}
.egg-container .list-group-item.active,
.egg-container .list-group-item.active:hover,
.egg-container .list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.egg-container .list-group-item.active .list-group-item-heading,
.egg-container .list-group-item.active:hover .list-group-item-heading,
.egg-container .list-group-item.active:focus .list-group-item-heading,
.egg-container .list-group-item.active .list-group-item-heading > small,
.egg-container .list-group-item.active:hover .list-group-item-heading > small,
.egg-container .list-group-item.active:focus .list-group-item-heading > small,
.egg-container .list-group-item.active .list-group-item-heading > .small,
.egg-container .list-group-item.active:hover .list-group-item-heading > .small,
.egg-container .list-group-item.active:focus .list-group-item-heading > .small {
  color: inherit;
}
.egg-container .list-group-item.active .list-group-item-text,
.egg-container .list-group-item.active:hover .list-group-item-text,
.egg-container .list-group-item.active:focus .list-group-item-text {
  color: #c7ddef;
}
.egg-container .list-group-item-success {
  color: #3c763d;
  background-color: #dff0d8;
}
a.egg-container .list-group-item-success {
  color: #3c763d;
}
a.egg-container .list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.egg-container .list-group-item-success:hover,
a.egg-container .list-group-item-success:focus {
  color: #3c763d;
  background-color: #d0e9c6;
}
a.egg-container .list-group-item-success.active,
a.egg-container .list-group-item-success.active:hover,
a.egg-container .list-group-item-success.active:focus {
  color: #fff;
  background-color: #3c763d;
  border-color: #3c763d;
}
.egg-container .list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}
a.egg-container .list-group-item-info {
  color: #31708f;
}
a.egg-container .list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.egg-container .list-group-item-info:hover,
a.egg-container .list-group-item-info:focus {
  color: #31708f;
  background-color: #c4e3f3;
}
a.egg-container .list-group-item-info.active,
a.egg-container .list-group-item-info.active:hover,
a.egg-container .list-group-item-info.active:focus {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}
.egg-container .list-group-item-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
}
a.egg-container .list-group-item-warning {
  color: #8a6d3b;
}
a.egg-container .list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.egg-container .list-group-item-warning:hover,
a.egg-container .list-group-item-warning:focus {
  color: #8a6d3b;
  background-color: #faf2cc;
}
a.egg-container .list-group-item-warning.active,
a.egg-container .list-group-item-warning.active:hover,
a.egg-container .list-group-item-warning.active:focus {
  color: #fff;
  background-color: #8a6d3b;
  border-color: #8a6d3b;
}
.egg-container .list-group-item-danger {
  color: #a94442;
  background-color: #f2dede;
}
a.egg-container .list-group-item-danger {
  color: #a94442;
}
a.egg-container .list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.egg-container .list-group-item-danger:hover,
a.egg-container .list-group-item-danger:focus {
  color: #a94442;
  background-color: #ebcccc;
}
a.egg-container .list-group-item-danger.active,
a.egg-container .list-group-item-danger.active:hover,
a.egg-container .list-group-item-danger.active:focus {
  color: #fff;
  background-color: #a94442;
  border-color: #a94442;
}
.egg-container .list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.egg-container .list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.egg-container .panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
}
.egg-container .panel-body {
  padding: 20px;
}
.egg-container .panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.egg-container .panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.egg-container .panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit;
}
.egg-container .panel-title > a,
.egg-container .panel-title > small,
.egg-container .panel-title > .small,
.egg-container .panel-title > small > a,
.egg-container .panel-title > .small > a {
  color: inherit;
}
.egg-container .panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.egg-container .panel > .list-group,
.egg-container .panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.egg-container .panel > .list-group .list-group-item,
.egg-container .panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.egg-container .panel > .list-group:first-child .list-group-item:first-child,
.egg-container .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.egg-container .panel > .list-group:last-child .list-group-item:last-child,
.egg-container .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.egg-container .panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.egg-container .list-group + .panel-footer {
  border-top-width: 0;
}
.egg-container .panel > .table,
.egg-container .panel > .table-responsive > .table,
.egg-container .panel > .panel-collapse > .table {
  margin-bottom: 0;
}
.egg-container .panel > .table caption,
.egg-container .panel > .table-responsive > .table caption,
.egg-container .panel > .panel-collapse > .table caption {
  padding-right: 15px;
  padding-left: 15px;
}
.egg-container .panel > .table:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.egg-container .panel > .table:first-child > thead:first-child > tr:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
.egg-container .panel > .table:first-child > tbody:first-child > tr:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.egg-container .panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.egg-container .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.egg-container .panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.egg-container .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 3px;
}
.egg-container .panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.egg-container .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.egg-container .panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.egg-container .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.egg-container .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 3px;
}
.egg-container .panel > .table:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.egg-container .panel > .table:last-child > tbody:last-child > tr:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
.egg-container .panel > .table:last-child > tfoot:last-child > tr:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.egg-container .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.egg-container .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.egg-container .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.egg-container .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 3px;
}
.egg-container .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.egg-container .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.egg-container .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.egg-container .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.egg-container .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 3px;
}
.egg-container .panel > .panel-body + .table,
.egg-container .panel > .panel-body + .table-responsive,
.egg-container .panel > .table + .panel-body,
.egg-container .panel > .table-responsive + .panel-body {
  border-top: 1px solid #ddd;
}
.egg-container .panel > .table > tbody:first-child > tr:first-child th,
.egg-container .panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.egg-container .panel > .table-bordered,
.egg-container .panel > .table-responsive > .table-bordered {
  border: 0;
}
.egg-container .panel > .table-bordered > thead > tr > th:first-child,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.egg-container .panel > .table-bordered > tbody > tr > th:first-child,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.egg-container .panel > .table-bordered > tfoot > tr > th:first-child,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.egg-container .panel > .table-bordered > thead > tr > td:first-child,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.egg-container .panel > .table-bordered > tbody > tr > td:first-child,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.egg-container .panel > .table-bordered > tfoot > tr > td:first-child,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.egg-container .panel > .table-bordered > thead > tr > th:last-child,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.egg-container .panel > .table-bordered > tbody > tr > th:last-child,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.egg-container .panel > .table-bordered > tfoot > tr > th:last-child,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.egg-container .panel > .table-bordered > thead > tr > td:last-child,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.egg-container .panel > .table-bordered > tbody > tr > td:last-child,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.egg-container .panel > .table-bordered > tfoot > tr > td:last-child,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.egg-container .panel > .table-bordered > thead > tr:first-child > td,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.egg-container .panel > .table-bordered > tbody > tr:first-child > td,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.egg-container .panel > .table-bordered > thead > tr:first-child > th,
.egg-container .panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.egg-container .panel > .table-bordered > tbody > tr:first-child > th,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
  border-bottom: 0;
}
.egg-container .panel > .table-bordered > tbody > tr:last-child > td,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.egg-container .panel > .table-bordered > tfoot > tr:last-child > td,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.egg-container .panel > .table-bordered > tbody > tr:last-child > th,
.egg-container .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.egg-container .panel > .table-bordered > tfoot > tr:last-child > th,
.egg-container .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.egg-container .panel > .table-responsive {
  margin-bottom: 0;
  border: 0;
}
.egg-container .panel-group {
  margin-bottom: 20px;
}
.egg-container .panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
}
.egg-container .panel-group .panel + .panel {
  margin-top: 5px;
}
.egg-container .panel-group .panel-heading {
  border-bottom: 0;
}
.egg-container .panel-group .panel-heading + .panel-collapse > .panel-body,
.egg-container .panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: 1px solid #ddd;
}
.egg-container .panel-group .panel-footer {
  border-top: 0;
}
.egg-container .panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ddd;
}
.egg-container .panel-default {
  border-color: #ddd;
}
.egg-container .panel-default > .panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd;
}
.egg-container .panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ddd;
}
.egg-container .panel-default > .panel-heading .badge {
  color: #f5f5f5;
  background-color: #333;
}
.egg-container .panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ddd;
}
.egg-container .panel-primary {
  border-color: #337ab7;
}
.egg-container .panel-primary > .panel-heading {
  color: #fff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.egg-container .panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #337ab7;
}
.egg-container .panel-primary > .panel-heading .badge {
  color: #337ab7;
  background-color: #fff;
}
.egg-container .panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #337ab7;
}
.egg-container .panel-success {
  border-color: #d6e9c6;
}
.egg-container .panel-success > .panel-heading {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.egg-container .panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #d6e9c6;
}
.egg-container .panel-success > .panel-heading .badge {
  color: #dff0d8;
  background-color: #3c763d;
}
.egg-container .panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #d6e9c6;
}
.egg-container .panel-info {
  border-color: #bce8f1;
}
.egg-container .panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.egg-container .panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #bce8f1;
}
.egg-container .panel-info > .panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f;
}
.egg-container .panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #bce8f1;
}
.egg-container .panel-warning {
  border-color: #faebcc;
}
.egg-container .panel-warning > .panel-heading {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.egg-container .panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #faebcc;
}
.egg-container .panel-warning > .panel-heading .badge {
  color: #fcf8e3;
  background-color: #8a6d3b;
}
.egg-container .panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #faebcc;
}
.egg-container .panel-danger {
  border-color: #ebccd1;
}
.egg-container .panel-danger > .panel-heading {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.egg-container .panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ebccd1;
}
.egg-container .panel-danger > .panel-heading .badge {
  color: #f2dede;
  background-color: #a94442;
}
.egg-container .panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ebccd1;
}
.egg-container .embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}
.egg-container .embed-responsive .embed-responsive-item,
.egg-container .embed-responsive iframe,
.egg-container .embed-responsive embed,
.egg-container .embed-responsive object,
.egg-container .embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
.egg-container .embed-responsive-16by9 {
  padding-bottom: 56.25%;
}
.egg-container .embed-responsive-4by3 {
  padding-bottom: 75%;
}
.egg-container .well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
}
.egg-container .well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, .15);
}
.egg-container .well-lg {
  padding: 24px;
  border-radius: 6px;
}
.egg-container .well-sm {
  padding: 9px;
  border-radius: 3px;
}
.egg-container .close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  filter: alpha(opacity=20);
  opacity: .2;
}
.egg-container .close:hover,
.egg-container .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  filter: alpha(opacity=50);
  opacity: .5;
}
button.egg-container .close {
  -webkit-appearance: none;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
}
.egg-container .clearfix:before,
.egg-container .clearfix:after,
.egg-container .dl-horizontal dd:before,
.egg-container .dl-horizontal dd:after,
.egg-container .container:before,
.egg-container .container:after,
.egg-container .container-fluid:before,
.egg-container .container-fluid:after,
.egg-container .row:before,
.egg-container .row:after,
.egg-container .form-horizontal .form-group:before,
.egg-container .form-horizontal .form-group:after,
.egg-container .btn-toolbar:before,
.egg-container .btn-toolbar:after,
.egg-container .btn-group-vertical > .btn-group:before,
.egg-container .btn-group-vertical > .btn-group:after,
.egg-container .nav:before,
.egg-container .nav:after,
.egg-container .panel-body:before,
.egg-container .panel-body:after {
  display: table;
  content: " ";
}
.egg-container .clearfix:after,
.egg-container .dl-horizontal dd:after,
.egg-container .container:after,
.egg-container .container-fluid:after,
.egg-container .row:after,
.egg-container .form-horizontal .form-group:after,
.egg-container .btn-toolbar:after,
.egg-container .btn-group-vertical > .btn-group:after,
.egg-container .nav:after,
.egg-container .panel-body:after {
  clear: both;
}
.egg-container .center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}
.egg-container .pull-right {
  float: right !important;
}
.egg-container .pull-left {
  float: left !important;
}
.egg-container .hide {
  display: none !important;
}
.egg-container .show {
  display: block !important;
}
.egg-container .invisible {
  visibility: hidden;
}
.egg-container .text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.egg-container .hidden {
  display: none !important;
}
.egg-container .affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.egg-container .visible-xs,
.egg-container .visible-sm,
.egg-container .visible-md,
.egg-container .visible-lg {
  display: none !important;
}
.egg-container .visible-xs-block,
.egg-container .visible-xs-inline,
.egg-container .visible-xs-inline-block,
.egg-container .visible-sm-block,
.egg-container .visible-sm-inline,
.egg-container .visible-sm-inline-block,
.egg-container .visible-md-block,
.egg-container .visible-md-inline,
.egg-container .visible-md-inline-block,
.egg-container .visible-lg-block,
.egg-container .visible-lg-inline,
.egg-container .visible-lg-inline-block {
  display: none !important;
}
@media (max-width: 767px) {
  .egg-container .visible-xs {
    display: block !important;
  }
  table.egg-container .visible-xs {
    display: table;
  }
  tr.egg-container .visible-xs {
    display: table-row !important;
  }
  th.egg-container .visible-xs,
  td.egg-container .visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .egg-container .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .egg-container .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 767px) {
  .egg-container .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .egg-container .visible-sm {
    display: block !important;
  }
  table.egg-container .visible-sm {
    display: table;
  }
  tr.egg-container .visible-sm {
    display: table-row !important;
  }
  th.egg-container .visible-sm,
  td.egg-container .visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .egg-container .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .egg-container .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .egg-container .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .egg-container .visible-md {
    display: block !important;
  }
  table.egg-container .visible-md {
    display: table;
  }
  tr.egg-container .visible-md {
    display: table-row !important;
  }
  th.egg-container .visible-md,
  td.egg-container .visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .egg-container .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .egg-container .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .egg-container .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1200px) {
  .egg-container .visible-lg {
    display: block !important;
  }
  table.egg-container .visible-lg {
    display: table;
  }
  tr.egg-container .visible-lg {
    display: table-row !important;
  }
  th.egg-container .visible-lg,
  td.egg-container .visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .egg-container .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .egg-container .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .egg-container .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 767px) {
  .egg-container .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .egg-container .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .egg-container .hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .egg-container .hidden-lg {
    display: none !important;
  }
}
.egg-container .visible-print {
  display: none !important;
}
@media print {
  .egg-container .visible-print {
    display: block !important;
  }
  table.egg-container .visible-print {
    display: table;
  }
  tr.egg-container .visible-print {
    display: table-row !important;
  }
  th.egg-container .visible-print,
  td.egg-container .visible-print {
    display: table-cell !important;
  }
}
.egg-container .visible-print-block {
  display: none !important;
}
@media print {
  .egg-container .visible-print-block {
    display: block !important;
  }
}
.egg-container .visible-print-inline {
  display: none !important;
}
@media print {
  .egg-container .visible-print-inline {
    display: inline !important;
  }
}
.egg-container .visible-print-inline-block {
  display: none !important;
}
@media print {
  .egg-container .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .egg-container .hidden-print {
    display: none !important;
  }
}
/*# sourceMappingURL=bootstrap.css.map */