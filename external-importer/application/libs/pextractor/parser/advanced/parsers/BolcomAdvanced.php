<?php

namespace ExternalImporter\application\libs\pextractor\parser\parsers;

defined('\ABSPATH') || exit;

use ExternalImporter\application\helpers\TextHelper;
use ExternalImporter\application\libs\pextractor\parser\Product;
use ExternalImporter\application\libs\pextractor\ExtractorHelper;

use function ExternalImporter\prnx;

/**
 * BolcomAdvanced class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class BolcomAdvanced extends AdvancedParser
{

    public function parseLinks()
    {
        $sponsored = $this->xpathArray(".//*[@class='dsa__label']/../../..//a[contains(@class, 'product-title')]/@href");

        $path = array(
            ".//a[contains(@class, 'product-title')]/@href",
            ".//a[@class='product-title px_list_page_product_click list_page_product_tracking_target']/@href",
            ".//a[@data-test='product-title']/@href",
        );

        $links = $this->xpathArray($path);

        return array_diff($links, $sponsored);
    }

    public function parsePagination()
    {
        $path = array(
            ".//ul[@class='pagination']//li/a/@href",
        );

        return $this->xpathArray($path);
    }

    public function parseDescription()
    {
        return $this->xpathScalar(".//div[@class='product-description']", true);
    }

    public function parseOldPrice()
    {
        $paths = array(
            ".//section[contains(@class, 'buy-block__prices')]//del[@class='buy-block__list-price']",
            ".//del[@class='h-nowrap buy-block__list-price']",
        );

        return $this->xpathScalar($paths);
    }

    public function parseImage()
    {
        if ($images = $this->parseImages())
            return reset($images);

        $paths = array(
            ".//meta[@property='og:image']/@content",
        );

        return $this->xpathScalar($paths);
    }

    public function parseImages()
    {
        if (preg_match_all('/"zoomImageUrl":"(https?:\/\/[^"]+)"/', $this->html, $matches))
        {
            foreach ($matches[1] as $i => $url)
            {
                $matches[1][$i] = json_decode('"' . $url . '"');
            }

            return $matches[1];
        }

        if (preg_match_all('/"imageUrl":"(.+?)"/ims', $this->html, $matches))
            return $matches[1];
    }

    public function getFeaturesXpath()
    {
        return array(
            array(
                'name' => ".//div[@class='specs']//dt/text()[normalize-space()]",
                'value' => ".//div[@class='specs']//dd",
            ),
        );
    }

    public function getReviewsXpath()
    {
        return array(
            array(
                'review' => ".//div[@class='reviews']//p[@data-test='review-body']",
                'rating' => ".//*[@class='reviews']//*[@class='star-rating']/span/@style",
                'author' => ".//*[@class='reviews']//ul[@class='review-metadata__list']/li[1]",
                'date' => ".//*[@class='reviews']//ul[@class='review-metadata__list']/li[3]",
            ),
        );
    }

    public function parseReviews()
    {
        $pros_cons_xpath = ".//div[@class='review-pros-and-cons']";

        if (!$xpaths = $this->getReviewsXpath())
            return array();

        if (isset($xpaths['review']))
            $xpaths = array($xpaths);

        foreach ($xpaths as $xpath)
        {
            $reviews = $ratings = $authors = $dates = array();

            if (!empty($xpath['review']))
                $reviews = $this->xpathArray($xpath['review'], true);

            if (!$reviews)
                continue;

            $pros_cons = $this->xpathArray($pros_cons_xpath, true);

            if (!empty($xpath['rating']))
                $ratings = $this->xpathArray($xpath['rating']);

            if (!empty($xpath['author']))
                $authors = $this->xpathArray($xpath['author']);

            if (!empty($xpath['date']))
                $dates = $this->xpathArray($xpath['date']);

            $results = array();
            for ($i = 0; $i < count($reviews); $i++)
            {
                $review = array();
                $review['review'] = \normalize_whitespace(TextHelper::sanitizeHtml(html_entity_decode($reviews[$i])));

                if (isset($pros_cons[$i]))
                    $review['review'] .= "\n" . \normalize_whitespace(TextHelper::sanitizeHtml(html_entity_decode($pros_cons[$i])));

                if (isset($ratings[$i]))
                {
                    if (strstr($ratings[$i], ':'))
                    {
                        $r_parts = explode(":", $ratings[$i]);
                        if (count($r_parts) == 2)
                            $ratings[$i] = $r_parts[1];
                    }

                    $review['rating'] = ExtractorHelper::ratingPrepare($ratings[$i]);
                }

                if (isset($authors[$i]))
                    $review['author'] = \sanitize_text_field(html_entity_decode($authors[$i]));

                if (isset($dates[$i]))
                    $review['date'] = strtotime($dates[$i]);

                $results[] = $review;
            }

            if ($results)
                return $results;
        }
        return array();
    }

    public function parseCurrencyCode()
    {
        return 'EUR';
    }

    public function afterParseFix(Product $product)
    {
        if ($product->availability == 'OutOfStock')
        {
            $product->price = null;
            $product->oldPrice = null;
        }

        foreach ($product->features as $f)
        {
            if ($f['name'] == 'EAN')
                $product->gtin = $f['value'];
        }

        return $product;
    }

    public function parseAvailability()
    {
        if ($this->xpathScalar(".//div[contains(@class, 'buy-block__options')]//a[@data-button-type='buy']"))
            return 'InStock';

        if ($this->xpathScalar(".//div[@class='u-pb--xs keep-me-updated']"))
            return 'OutOfStock';
    }

    public function parsePrice()
    {
        $path = array(
            ".//span[@data-test='price']",
        );

        if ($p = $this->xpathScalar($path))
        {
            $p = sanitize_text_field($p);
            $p = str_replace(' ', '.', $p);
            return $p;
        }
    }
}
