<?php

namespace ExternalImporter\application\libs\pextractor\parser\parsers;

defined('\ABSPATH') || exit;

/**
 * G2acomAdvanced class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class G2acomAdvanced extends AdvancedParser
{

    public function getHttpOptions()
    {
        $httpOptions = parent::getHttpOptions();

        // reset session cookies
        $httpOptions['cookies'] = array();
        $httpOptions['headers'] = array(
            'Accept' => '', //!!!
            'Accept-Language' => 'en-us,en;q=0.5',
            'Cache-Control' => 'no-cache',
        );
        $httpOptions['user-agent'] = 'ia_archiver';

        return $httpOptions;
    }

    public function parseLinks()
    {
        $path = array(
            ".//h3[contains(@class, 'sc-iqAclL')]/a/@href",

            ".//h3[contains(@class, 'indexes__Title')]/a/@href",
            ".//h3[@class='Card__title']/a/@href",
        );

        return $this->xpathArray($path);
    }

    public function parsePagination()
    {
        $path = array(
            ".//nav[@class='pagination']//a[contains(@href, 'page=')]/@href",
            ".//nav[contains(@class, 'indexes')]//a[contains(@href, 'page=')]/@href",
        );

        return $this->xpathArray($path);
    }

    public function parsePrice()
    {
        $paths = array(
            ".//span[@data-locator='zth-price']",
            ".//form[contains(@class, 'PaymentsRadiostyles__FormWrapper')]//*[@data-locator='zth-price']",
        );

        if ($p = $this->xpathScalar($paths))
            return $p;
    }

    public function parseOldPrice()
    {
        $paths = array(
            ".//span[@data-locator='zth-price']/text()",
            ".//div[contains(@class, 'product-info__payments')]//span[@class='product-page-v2-price__old-price']",
            ".//div[contains(@class, 'indexes__PriceDetails')]//s",
        );

        if ($p = $this->xpathScalar($paths))
            return $p;

        if (preg_match('/"suggestedPrice":"(.+?)"/', $this->html, $matches))
        {
            return $matches[1];
        }
    }

    public function parseDescription()
    {
        $paths = array(
            ".//div[@data-locator='product-description']",
        );

        return $this->xpathScalar($paths, true);
    }

    public function parseImage()
    {
        $paths = array(
            ".//div[contains(@class, 'indexes__StyledGalleryItem')]//img/@src",
            ".//meta[@property='og:image']/@content",
        );

        return $this->xpathArray($paths);
    }

    public function parseImages()
    {
        $paths = array(
            ".//div[contains(@class, 'page--product2__gallery--digital')]//img[not(contains(@data-src, 'youtube'))]/@data-src",
        );

        return $this->xpathArray($paths);
    }

    public function getFeaturesXpath()
    {
        return array(
            array(
                'name' => ".//div[@id='collapsible-panel-details']//div[@class='text-body3 leading-6 text-neutral-700']",
                'value' => ".//div[@id='collapsible-panel-details']//div[@class='self-end  text-body3 leading-6 text-neutral-900']",
            ),
            array(
                'name' => ".//*[@class='attributes-list']//span[@class='attributes-list__name']",
                'value' => ".//*[@class='attributes-list']//span[@class='attributes-list__value']",
            ),
        );
    }
}
