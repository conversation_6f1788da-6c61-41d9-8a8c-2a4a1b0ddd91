<?php

namespace ExternalImporter\application\components\ai;

defined('\ABSPATH') || exit;

/**
 * PromptHelper class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */

class PromptHelper
{
    public static function build($prompt, array $params)
    {
        $replace = array();

        foreach ($params as $name => $value)
        {
            $replace['%' . $name . '%'] = $value;
        }

        return str_ireplace(array_keys($replace), array_values($replace), $prompt);
    }
}
