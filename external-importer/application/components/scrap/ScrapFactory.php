<?php

namespace ExternalImporter\application\components\scrap;

defined('\ABSPATH') || exit;

use ExternalImporter\application\components\scrap\ProxycrawlScrap;
use ExternalImporter\application\components\scrap\ScraperapiScrap;
use ExternalImporter\application\components\scrap\ScrapingdogScrap;

/**
 * ScrapFactory class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class ScrapFactory
{

    public static $services = array(
        ProxycrawlScrap::SLUG => ProxycrawlScrap::class,
        ScraperapiScrap::SLUG => ScraperapiScrap::class,
        ScrapingdogScrap::SLUG => ScrapingdogScrap::class,
        ScrapeowlScrap::SLUG => ScrapeowlScrap::class,
    );

    public static function init()
    {
        foreach (self::$services as $slug => $class)
        {
            $scrap = new $class();
            $scrap->initAction();
        }
    }
}
