<?php

namespace ExternalImporter\application\components\scrap;

use function ExternalImporter\prn;
use function ExternalImporter\prnx;

defined('\ABSPATH') || exit;

/**
 * ScraperapiScrap class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class ScraperapiScrap extends Scrap
{
    const SLUG = 'scraperapi';

    public function doAction($url, $args)
    {
        if (!$this->needSendThrough($url))
            return $url;

        $url = 'http://api.scraperapi.com?api_key=' . urlencode($this->getToken()) . '&url=' . urlencode($url) . '&keep_headers=false';

        if (strstr($url, 'etsy.com%2Fsearch'))
            $url .= '&ultra_premium=true';
        elseif (strstr($url, 'www.bol.com'))
            $url .= '&ultra_premium=true';

        $url = \apply_filters('ei_parse_url_' . $this->getSlug(), $url);

        return $url;
    }
}
