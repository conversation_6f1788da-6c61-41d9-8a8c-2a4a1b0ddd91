<?php

namespace ExternalImporter\application\components\scrap;

use function ExternalImporter\prnx;

defined('\ABSPATH') || exit;

/**
 * ScrapingdogScrap class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class ScrapingdogScrap extends Scrap
{
    const SLUG = 'scrapingdog';

    public function doAction($url, $args)
    {
        if (!$this->needSendThrough($url))
            return $url;

        $url = 'https://api.scrapingdog.com/scrape?api_key=' . urlencode($this->getToken()) . '&url=' . urlencode($url) . '&dynamic=false';
        $url = \apply_filters('ei_parse_url_' . $this->getSlug(), $url);

        return $url;
    }
}
