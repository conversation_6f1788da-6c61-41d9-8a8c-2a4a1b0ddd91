<?php

namespace ExternalImporter\application;

defined('\ABSPATH') || exit;

use ExternalImporter\application\admin\DeeplinkConfig;
use ExternalImporter\application\components\WooImporter;
use ExternalImporter\application\components\LinkHandler;
use ExternalImporter\application\admin\FrontendConfig;
use ExternalImporter\application\Redirect;
use ExternalImporter\application\admin\LicConfig;

use function ExternalImporter\prn;

/**
 * LinkProcessor class file
 *
 * <AUTHOR> <<EMAIL>>
 * @link https://www.keywordrush.com
 * @copyright Copyright &copy; 2025 keywordrush.com
 */
class LinkProcessor
{

    public static function initAction()
    {
        $l = LicConfig::getInstance()->option('lic' . 'ense' . '_key');
        if (strlen($l) != 32 && strlen($l) != 36)
            return;

        \add_filter('woocommerce_product_add_to_cart_url', array(__CLASS__, 'process'), 10, 2);
    }

    public static function process($permalink, $product)
    {
        if ($product->get_type() != 'external')
            return $permalink;

        if (!$url = WooImporter::getProductUrlMeta($product->get_id()))
            return $permalink;

        $d = \get_option(base64_decode('ZXhpX3N5c19kZWFkbGluZQ=='), 0);
        if ($d && $d < time())
            return $permalink;

        // URL changed?
        if (!self::compareUrls($product->get_product_url(), $url))
            return $permalink;

        // local redirect enabled?
        if (FrontendConfig::getInstance()->option('local_redirect'))
            return self::generateRedirectedUrl($product->get_id());

        return self::generateAffiliateUrl($url);
    }

    public static function generateRedirectedUrl($product_id)
    {
        $prefix = Redirect::getPrefix();
        if (\get_option('permalink_structure'))
            $path = urlencode($prefix) . '/';
        else
            $path = '?' . urlencode($prefix) . '=';

        $path .= urlencode($product_id);

        return \get_site_url(\get_current_blog_id(), $path);
    }

    public static function generateAffiliateUrl($url)
    {
        if (!$deeplink = DeeplinkConfig::getInstance()->getDeeplinkByUrl($url))
            return $url;

        return LinkHandler::createAffUrl($url, $deeplink);
    }

    public static function compareUrls($url1, $url2)
    {
        $parts1 = parse_url($url1);
        $parts2 = parse_url($url2);

        $host1 = isset($parts1['host']) ? strtolower($parts1['host']) : '';
        $host2 = isset($parts2['host']) ? strtolower($parts2['host']) : '';

        if ($host1 !== $host2)
            return false;

        $path1 = isset($parts1['path']) ? rtrim($parts1['path'], '/') : '';
        $path2 = isset($parts2['path']) ? rtrim($parts2['path'], '/') : '';
        if ($path1 !== $path2)
            return false;

        $query1 = isset($parts1['query']) ? $parts1['query'] : '';
        $query2 = isset($parts2['query']) ? $parts2['query'] : '';
        parse_str($query1, $params1);
        parse_str($query2, $params2);

        if ($params1 !== $params2)
            return false;

        return true;
    }
}
