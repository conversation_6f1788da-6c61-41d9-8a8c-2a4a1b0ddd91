[byterover-mcp]

# important

always use byterover-retrieve-knowledge tool to get the related context before any tasks
always use byterover-store-knowledge to store all the critical informations after sucessful tasks

# External Importer Pro to Full-Stack JavaScript Conversion Prompt

## Project Overview

Convert the External Importer Pro WordPress plugin from PHP to a modern full-stack JavaScript application. The application should replicate all core functionality while providing a modern, scalable architecture focused on Amazon India product scraping with configurable scraper API integration.

## Original Plugin Analysis

### Core Functionality (PHP-based)

- **Product Extraction Engine**: Multi-layered parsing system with structured data parsers, advanced parsers for 90+ stores, AI parsers trained on 1000+ sites, and custom parser development
- **Data Extraction**: Title, Image, Price, Currency, Availability, Description, Gallery images, Old price, Brand, Category path, Specifications, User reviews, GTIN
- **Import Methods**: Individual product URLs, listing pages with pagination, bulk category imports, automatic pagination support
- **WooCommerce Integration**: Simple/affiliate products, auto-generated affiliate links, custom margins, scheduled updates

### Technical Architecture (PHP)

- **Parser Classes**: PHP classes extending AdvancedParser with methods like parseTitle(), parsePrice(), parseImages(), etc.
- **Namespace**: ExternalImporter\application\libs\pextractor\parser\parsers
- **Database**: WordPress custom post types, taxonomies, meta fields
- **Debug Mode**: Request caching, parser identification, raw data display

## Target JavaScript Application Requirements

### Technology Stack

- **Frontend**: React.js/Next.js with TypeScript
- **Backend**: Node.js with Express.js or Fastify
- **Database**: MongoDB or PostgreSQL
- **Scraping**: Puppeteer/Playwright + Cheerio + Axios
- **State Management**: Redux Toolkit or Zustand
- **UI Framework**: Material-UI, Chakra UI, or Tailwind CSS

### Application Features

#### Core Scraping Engine

1. **Multi-Parser System**

   - Structured Data Parser (JSON-LD, Microdata, Open Graph, Twitter Card)
   - Advanced Parser (site-specific selectors)
   - AI Parser (intelligent element detection)
   - Custom Parser (user-defined selectors)

2. **Amazon India Focus**

   - Specialized Amazon.in parser
   - Product URL validation
   - Dynamic content handling
   - Anti-blocking measures
   - Rate limiting and proxy support

3. **Scraper API Integration**
   - Configurable API key management
   - Support for ScrapingBee, ScraperAPI, or similar services
   - Fallback to direct scraping
   - Request rotation and retry logic

#### Frontend Components

1. **Dashboard Component**

   ```jsx
   // Main interface showing scraping statistics, recent products, and quick actions
   - Product count widgets
   - Recent scraping activity
   - Quick URL input
   - Settings access
   ```

2. **ProductImporter Component**

   ```jsx
   // URL input interface with parser selection
   - URL input field with validation
   - Parser type selection (auto/manual)
   - Batch URL import (textarea for multiple URLs)
   - Preview mode toggle
   - Scraping progress indicator
   ```

3. **ProductList Component**

   ```jsx
   // Display extracted products with filtering and sorting
   - Grid/list view toggle
   - Search and filter options
   - Bulk selection checkboxes
   - Pagination controls
   - Sort by price, date, category
   ```

4. **ProductPreview Component**

   ```jsx
   // Individual product view with edit capabilities
   - Product image gallery
   - Editable fields (title, description, price)
   - Category assignment
   - Save/reject buttons
   - Source URL link
   ```

5. **SettingsPanel Component**
   ```jsx
   // Configuration interface
   - Scraper API key input
   - Amazon domain selection
   - Default parser preferences
   - Auto-sync intervals
   - Proxy settings
   ```

#### Backend API Structure

1. **Scraping Endpoints**

   ```javascript
   POST /api/scrape/single
   // Scrape single product URL
   // Body: { url, parserType, apiKey }
   // Response: { product, success, errors }

   POST /api/scrape/batch
   // Scrape multiple URLs
   // Body: { urls: [], parserType, apiKey }
   // Response: { products: [], failed: [], statistics }

   GET /api/scrape/status/:jobId
   // Check scraping job status
   // Response: { status, progress, results }
   ```

2. **Product Management**

   ```javascript
   GET /api/products
   // Retrieve products with filtering and pagination
   // Query: ?page=1&limit=20&category=electronics&approved=false

   POST /api/products/:id/approve
   // Approve/reject scraped product
   // Body: { approved: boolean, modifications: {} }

   PUT /api/products/:id
   // Update product information
   // Body: { title, description, price, category, etc. }

   DELETE /api/products/:id
   // Delete product

   POST /api/products/bulk-action
   // Bulk approve/reject/delete
   // Body: { action: 'approve'|'reject'|'delete', productIds: [] }
   ```

3. **Parser Management**

   ```javascript
   GET / api / parsers;
   // Get available parsers
   // Response: { parsers: [{ name, domain, type, isActive }] }

   POST / api / parsers / custom;
   // Create custom parser
   // Body: { name, domain, selectors: {} }

   GET / api / parsers / test;
   // Test parser on URL
   // Query: ?url=...&parserId=...
   ```

#### Database Schema

1. **Products Collection/Table**

   ```javascript
   {
     _id: ObjectId,
     url: String, // Source URL
     title: String,
     description: String,
     price: Number,
     oldPrice: Number,
     currency: String,
     availability: Boolean,
     images: [String], // Array of image URLs
     brand: String,
     category: String,
     specifications: Object, // Key-value pairs
     reviews: [{
       rating: Number,
       comment: String,
       reviewer: String,
       date: Date
     }],
     gtin: String,
     source: String, // 'amazon.in', 'flipkart.com', etc.
     parserUsed: String,
     scrapedAt: Date,
     updatedAt: Date,
     isApproved: Boolean,
     approvedAt: Date,
     metadata: Object // Additional scraped data
   }
   ```

2. **Parsers Collection/Table**

   ```javascript
   {
     _id: ObjectId,
     name: String,
     domain: String,
     type: String, // 'structured', 'advanced', 'ai', 'custom'
     selectors: {
       title: String,
       price: String,
       description: String,
       images: String,
       availability: String,
       brand: String,
       specifications: String
     },
     isActive: Boolean,
     createdAt: Date,
     successRate: Number // Track parser effectiveness
   }
   ```

3. **Settings Collection/Table**
   ```javascript
   {
     _id: ObjectId,
     scraperApiKey: String,
     scraperService: String, // 'scrapingbee', 'scraperapi', 'direct'
     amazonDomain: String, // 'amazon.in'
     autoSync: Boolean,
     syncInterval: String, // '1h', '6h', '24h'
     proxySettings: Object,
     notificationSettings: Object,
     defaultParser: String
   }
   ```

#### Core Scraping Logic

1. **Parser Factory Pattern**

   ```javascript
   class ParserFactory {
     static createParser(type, domain) {
       switch (type) {
         case "structured":
           return new StructuredDataParser();
         case "advanced":
           return new AdvancedParser(domain);
         case "ai":
           return new AIParser();
         case "custom":
           return new CustomParser();
         default:
           return new StructuredDataParser();
       }
     }
   }
   ```

2. **Base Parser Class**

   ```javascript
   class BaseParser {
     async parseProduct(url, html, options = {}) {
       return {
         title: await this.parseTitle(html),
         price: await this.parsePrice(html),
         description: await this.parseDescription(html),
         images: await this.parseImages(html),
         availability: await this.parseAvailability(html),
         brand: await this.parseBrand(html),
         specifications: await this.parseSpecifications(html),
         reviews: await this.parseReviews(html),
       };
     }
   }
   ```

3. **Amazon India Specific Parser**
   ```javascript
   class AmazonIndiaParser extends BaseParser {
     constructor() {
       super();
       this.domain = "amazon.in";
       this.selectors = {
         title: "#productTitle",
         price: ".a-price-whole",
         description: "#feature-bullets ul",
         images: "#altImages img",
         availability: "#availability span",
         brand: "#bylineInfo",
         specifications: "#technicalSpecifications_section_1",
       };
     }
   }
   ```

#### Scraper API Integration

1. **ScrapingBee Integration**

   ```javascript
   class ScrapingBeeClient {
     constructor(apiKey) {
       this.apiKey = apiKey;
       this.baseUrl = "https://app.scrapingbee.com/api/v1/";
     }

     async scrapeUrl(url, options = {}) {
       const params = {
         api_key: this.apiKey,
         url: url,
         render_js: options.renderJs || true,
         premium_proxy: options.premiumProxy || true,
         country_code: "in",
       };

       const response = await axios.get(this.baseUrl, { params });
       return response.data;
     }
   }
   ```

2. **Direct Scraping Fallback**

   ```javascript
   class DirectScraper {
     async scrapeUrl(url, options = {}) {
       const browser = await puppeteer.launch({
         headless: true,
         args: ["--no-sandbox", "--disable-setuid-sandbox"],
       });

       const page = await browser.newPage();
       await page.setUserAgent(
         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
       );
       await page.goto(url, { waitUntil: "networkidle2" });

       const html = await page.content();
       await browser.close();

       return html;
     }
   }
   ```

#### Background Jobs and Scheduling

1. **Job Queue System (using Bull or Agenda)**

   ```javascript
   const Queue = require("bull");
   const scrapeQueue = new Queue("scrape products");

   scrapeQueue.process(async (job) => {
     const { urls, parserType, apiKey } = job.data;
     const results = await scrapeMultipleProducts(urls, parserType, apiKey);
     return results;
   });
   ```

2. **Auto-sync Scheduler**

   ```javascript
   const cron = require("node-cron");

   // Daily price updates
   cron.schedule("0 6 * * *", async () => {
     const approvedProducts = await Product.find({ isApproved: true });
     await scrapeQueue.add("update-prices", { products: approvedProducts });
   });
   ```

#### User Interface Features

1. **Real-time Updates**

   ```javascript
   // WebSocket integration for live scraping progress
   const io = require("socket.io")(server);

   io.on("connection", (socket) => {
     socket.on("start-scraping", (data) => {
       // Start scraping job and emit progress updates
       scrapeProducts(data.urls).on("progress", (progress) => {
         socket.emit("scraping-progress", progress);
       });
     });
   });
   ```

2. **Responsive Design**
   - Mobile-friendly interface
   - Touch-optimized controls
   - Progressive Web App (PWA) capabilities
   - Offline functionality for viewing saved products

#### Security and Performance

1. **Rate Limiting**

   ```javascript
   const rateLimit = require("express-rate-limit");

   const scrapeLimit = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100, // limit each IP to 100 requests per windowMs
     message: "Too many scraping requests, please try again later.",
   });

   app.use("/api/scrape", scrapeLimit);
   ```

2. **Data Validation**

   ```javascript
   const joi = require("joi");

   const productSchema = joi.object({
     url: joi.string().uri().required(),
     parserType: joi
       .string()
       .valid("auto", "structured", "advanced", "ai", "custom"),
     apiKey: joi.string().optional(),
   });
   ```

#### Error Handling and Logging

1. **Comprehensive Error Handling**

   ```javascript
   class ScrapingError extends Error {
     constructor(message, type, url) {
       super(message);
       this.type = type; // 'PARSER_ERROR', 'NETWORK_ERROR', 'API_ERROR'
       this.url = url;
       this.timestamp = new Date();
     }
   }
   ```

2. **Logging System**

   ```javascript
   const winston = require("winston");

   const logger = winston.createLogger({
     level: "info",
     format: winston.format.combine(
       winston.format.timestamp(),
       winston.format.json()
     ),
     transports: [
       new winston.transports.File({ filename: "error.log", level: "error" }),
       new winston.transports.File({ filename: "combined.log" }),
     ],
   });
   ```

## Development Implementation Plan

### Phase 1: Core Backend Development (Week 1-2)

1. Set up Node.js project structure
2. Implement basic Express.js API endpoints
3. Create database models and connections
4. Develop core scraping engine with Puppeteer
5. Implement basic parsers (structured data and Amazon India)

### Phase 2: Frontend Development (Week 3-4)

1. Set up React.js project with TypeScript
2. Create core components (Dashboard, ProductImporter, ProductList)
3. Implement state management with Redux Toolkit
4. Design responsive UI with chosen framework
5. Add real-time updates with WebSocket

### Phase 3: Advanced Features (Week 5-6)

1. Integrate scraper API services (ScrapingBee, ScraperAPI)
2. Implement background job processing
3. Add bulk operations and scheduling
4. Create custom parser builder
5. Implement data export/import functionality

### Phase 4: Testing and Optimization (Week 7-8)

1. Write comprehensive unit and integration tests
2. Performance optimization and caching
3. Security audit and penetration testing
4. Documentation and deployment guides
5. User acceptance testing

## Key Considerations

### Legal and Ethical

- Respect robots.txt and terms of service
- Implement appropriate delays between requests
- Use official APIs when available
- Consider data privacy regulations

### Performance

- Implement caching strategies
- Use connection pooling for database
- Optimize image handling and storage
- Implement pagination for large datasets

### Scalability

- Design for horizontal scaling
- Use microservices architecture if needed
- Implement proper load balancing
- Consider using Redis for session management

### Monitoring

- Implement application monitoring (New Relic, DataDog)
- Set up error tracking (Sentry)
- Create dashboards for scraping statistics
- Alert system for failed scraping jobs

## Deployment Architecture

### Development Environment

- Docker containers for easy setup
- Hot reloading for both frontend and backend
- Local database instance
- Environment variable management

### Production Environment

- Kubernetes or Docker Swarm orchestration
- Load balancer (Nginx or HAProxy)
- Database clustering
- CDN for static assets
- Monitoring and logging infrastructure

This comprehensive conversion plan transforms the PHP-based External Importer Pro into a modern, scalable JavaScript application while maintaining all core functionality and adding new capabilities specific to Amazon India scraping with configurable API integration.
